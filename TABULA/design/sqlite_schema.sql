/*
 Navicat Premium Data Transfer

 Source Server         : CRM 2025
 Source Server Type    : SQLite
 Source Server Version : 3035005 (3.35.5)
 Source Schema         : main

 Target Server Type    : SQLite
 Target Server Version : 3035005 (3.35.5)
 File Encoding         : 65001

 Date: 25/05/2025 15:36:39
*/

PRAGMA foreign_keys = false;

-- ----------------------------
-- Table structure for crm
-- ----------------------------
DROP TABLE IF EXISTS "crm";
CREATE TABLE "crm" (
  "id" INTEGER,
  "created_time" DATE,
  "ad_id" TEXT,
  "ad_name" TEXT,
  "adset_id" TEXT,
  "adset_name" TEXT,
  "campaign_id" TEXT,
  "campaign_name" TEXT,
  "form_id" TEXT,
  "form_name" TEXT,
  "is_organic" INTEGER,
  "op_history" TEXT,
  "platform" TEXT,
  "business_or_home_user?" TEXT,
  "nome" TEXT,
  "phone_number" TEXT,
  "email" TEXT,
  "lead_status" TEXT,
  "priority" INTEGER,
  "last_activity" DATE,
  "mail_count" INTEGER,
  "cust_activity" INTEGER,
  "wa_activity" INTEGER,
  "op" TEXT,
  "crm_mail" TEXT,
  "remind" DATE,
  "stato" TEXT,
  "notes" TEXT,
  "todo" TEXT
);

-- ----------------------------
-- Table structure for wa_messages
-- ----------------------------
DROP TABLE IF EXISTS "wa_messages";
CREATE TABLE "wa_messages" (
  "phone_number" text NOT NULL,
  "date" DATE,
  "from_me" blob,
  "message" TEXT
);

-- ----------------------------
-- View structure for CRM Active
-- ----------------------------
DROP VIEW IF EXISTS "CRM Active";
CREATE VIEW "CRM Active" AS SELECT
	crm.email, 
	crm.nome, 
	crm.phone_number, 
	crm.last_activity, 
	crm.mail_count, 
	crm.cust_activity, 
	crm.wa_activity, 
	crm.op, 
	crm.crm_mail, 
	crm.remind, 
	crm.stato, 
	crm.notes, 
	crm.todo, 
	crm.op_history, 
	crm.campaign_name
FROM
	crm
WHERE
	((cust_activity>0) OR (wa_activity>0))
ORDER BY
	last_activity DESC;

-- ----------------------------
-- View structure for CRM Full
-- ----------------------------
DROP VIEW IF EXISTS "CRM Full";
CREATE VIEW "CRM Full" AS SELECT
	crm.email, 
	crm.nome, 
	crm.phone_number, 
	crm.last_activity, 
	crm.mail_count, 
	crm.cust_activity, 
	crm.wa_activity, 
	crm.op, 
	crm.crm_mail, 
	crm.remind, 
	crm.stato, 
	crm.notes, 
	crm.todo, 
	crm.op_history, 
	crm.campaign_name
FROM
	crm
	ORDER BY last_activity DESC;

-- ----------------------------
-- View structure for CRM inactive
-- ----------------------------
DROP VIEW IF EXISTS "CRM inactive";
CREATE VIEW "CRM inactive" AS SELECT
	crm.email, 
	crm.nome, 
	crm.phone_number, 
	crm.last_activity, 
	crm.mail_count, 
	crm.cust_activity, 
	crm.wa_activity, 
	crm.op, 
	crm.crm_mail, 
	crm.remind, 
	crm.stato, 
	crm.notes, 
	crm.todo, 
	crm.op_history, 
	crm.campaign_name
FROM
	crm
WHERE
	((crm.mail_count IS NULL OR crm.mail_count <=1) AND (cust_activity IS NULL OR cust_activity<1) AND (wa_activity IS NULL OR wa_activity <1))
ORDER BY
	last_activity DESC;

PRAGMA foreign_keys = true;
