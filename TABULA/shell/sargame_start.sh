echo "Starting Ollama"
ollama run gemma2:2b "hello"

echo "Starting waha (docker)"
docker start waha

sleep 15
echo "Starting waha session"
curl -s -X POST -H "Content-Type: application/json" -d "{\"name\":\"default\",\"config\":{\"webhooks\":[{\"url\":\"http://host.docker.internal:50000/waha-webhook\",\"events\":[\"message\",\"message.any\"]}]}}" "http://localhost:3000/api/sessions/default/start"
# sleep 10
curl -s -X PUT -H "Content-Type: application/json" -d "{\"name\":\"default\",\"config\":{\"webhooks\":[{\"url\":\"http://host.docker.internal:50000/waha-webhook\",\"events\":[\"message\",\"message.any\"]}]}}" "http://localhost:3000/api/sessions/default"

cd  GIT/tabula.scripts/TABULA/shell/

echo "starting waha webhook"
screen -d -S WAHA -m ./sargame_waha_wh_run.sh

echo "starting CRM"
screen -d -S SARGAME -m ./sargame_crm_run.sh
