#!/bin/bash
# WhatsApp Webhook Runner for SARGame
# This script starts the WhatsApp webhook receiver for SARGame

# Default values
HOST="0.0.0.0"
PORT=50000
ENDPOINT="/waha-webhook"
DEBUG=""

# activate venv
source ../.venv/bin/activate

# Echo information
echo "Starting SARGame WhatsApp webhook server..."

cd ../tasks

# Run the webhook receiver
python3 "waha_receive.py" \
    --host "$HOST" \
    --port "$PORT" \
    --endpoint "$ENDPOINT" \
    --events "message" "message.any" \
    $DEBUG

# Note: The script will continue to run until interrupted with Ctrl+C
