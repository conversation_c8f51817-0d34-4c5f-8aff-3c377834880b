#!/bin/bash
# MAIN CRM PROCESSSING

# activate venv
source ../.venv/bin/activate

# Move to tasks folder
cd ../tasks

# Check and ensure <PERSON>lla<PERSON> is running using the dedicated utility script
bash ./check_ollama.sh
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to ensure Ollama is operational. Aborting CRM processing."
    exit 1
fi

echo
echo "####################### CHECKING TOKEN #######################"
# Check if the Google API token is valid
python3 check_token.py
TOKEN_STATUS=$?

# If the token is invalid, abort the script
if [ $TOKEN_STATUS -eq 1 ]; then
    echo "ERROR: Google API token is invalid. Aborting CRM processing."
    echo "Please run the refresh_token.py script to get a new token."
    exit 1
fi

echo
echo "####################### UPDATING WEB FORMS #######################"
# Populate web leads from gmail received forms (meta leads do this automatically)
# Uses state tracking to only process new form submissions since last run
python3 sargame_landing_webform.py --state "sargame_crm_webforms" --sender "<EMAIL>" --subject "S-ARGAME Landing Contact Request" --batch

echo
echo "####################### PULLING NEW LEADS #######################"
# Getting new contacts from auto leads table (both web and meta)
python3 pull_contacts.py

echo
echo "####################### PROCESSING CRM #######################"
# Process new emails from leads and decide the action ("op") that will be taken in next steps
python3 sargame_CRM.py --state "sargame_crm1" --label "02_S-ARGAME/LEADS" --batch

echo
echo "####################### OPERATIONS: SENDING EMAILS / WHATSAPP ###############"
# Execute the operations, mainy sending emails and whatsapp messages based on "op" column
python3 execute_operations.py

echo
echo "####################### UPDATE WHATSAPP STATS #######################"
# Reads the whatsapp_numbers.txt file and updates the CRM sheet with the number of times each phone number was used
python3 update_wa_stats.py

echo
echo "####################### FINALIZING CRM #######################"
# Re-runs the crm process to capture newly sent emails and update final status
# Uses separate state file to make this incremental rather than processing all emails
python3 sargame_CRM.py --state "sargame_crm_finalize" --label "02_S-ARGAME/LEADS" --batch

echo
echo "####################### SAVE SHEET TO SQLITE #######################"
# Transforms the CRM sheet into a SQLite database for faster querying, and selectively updates the static one
python3 get_crm_sqlite.py


echo
echo "####################### SEND DAILY DIGEST #######################"
# Sends a daily digest email with CRM statistics and hot leads
python3 send_daily_digest.py
