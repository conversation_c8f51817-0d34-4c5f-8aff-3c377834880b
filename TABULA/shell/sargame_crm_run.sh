#!/bin/bash

# SARGAME CRM Scheduler
# This script runs the CRM processing script hourly in a screen session

# Set logs directory to the existing path
LOG_DIR="$(dirname "$0")/../logs"
LOG_FILE="${LOG_DIR}/sargame_crm.log"
LAST_LOG_FILE="${LOG_DIR}/sargame_crm_last.log"

# No need to create the directory since it already exists
# Just log a message about using the existing log directory
echo "Using log directory at $LOG_DIR"

# Function to log messages to both console and log file
log_message() {
    echo "$@" | tee -a "$LOG_FILE"
}

log_message "Starting SARGAME CRM scheduler at $(date)"
log_message "Press Ctrl+C to stop the scheduler"
log_message "--------------------------------------------"

# Loop forever
while true; do
    # Display timestamp for logging
    log_message "--------------------------------------------"
    log_message "Running SARGAME CRM process at $(date)"
    log_message "--------------------------------------------"
    
    # Execute the CRM script and log its output
    bash "$(dirname "$0")/sargame_crm.sh" 2>&1 | tee >(tee -a "$LOG_FILE") > "$LAST_LOG_FILE"
    
    # Check if the script executed successfully
    if [ $? -eq 0 ]; then
        log_message "CRM process completed successfully"
    else
        log_message "WARNING: CRM process exited with an error"
    fi
    
    next_run=$(date -v+5M)
    log_message "Next run scheduled at $next_run"
    log_message "--------------------------------------------"
    
    # Sleep for 5 minutes
    sleep 300
done
