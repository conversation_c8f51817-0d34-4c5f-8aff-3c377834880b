#!/usr/bin/env python3
"""
Test script for the ollama module's Italian name detection functionality.
This script tests various names and emails to see if they are detected as Italian.
"""

import sys
import os
from pathlib import Path

# Import the ollama module directly
# Option 1: Add script directory to path (this will work if you run the script from anywhere)
script_dir = os.path.dirname(os.path.abspath(__file__))
tabula_scripts_dir = os.path.abspath(os.path.join(script_dir, '../../../'))
sys.path.insert(0, tabula_scripts_dir)

# Import the function from the ollama module
from TABULA.utils.waha import send_whatsapp_message

if __name__ == "__main__":
   send_whatsapp_message("+393338208586", "Hello from the test script!")