# Gmail Email Selection Test Tool

## Overview

The `test_gmail_selection.py` tool is a comprehensive test utility for Gmail email selection functionality. It uses the same parameters and configuration as found in `sargame_crm.sh` and tests the Gmail email selection/filtering capabilities of the `gmail_process.py` utility.

## Features

- **Flexible Timestamp Input**: Accepts timestamps in multiple formats:
  - DD/MM/YYYY (e.g., "15/12/2024")
  - YYYY-MM-DD (e.g., "2024-12-15") 
  - Unix timestamp in seconds (e.g., "1734220800")
  - No timestamp filter (shows recent messages)

- **Gmail API Integration**: Uses the same Gmail API calls as the production CRM system

- **Thread Visualization**: Groups emails by conversation threads and displays them chronologically

- **Lead Identification**: Automatically identifies lead email addresses (non-company domains)

- **Pretty Table Output**: Displays results in formatted tables with columns for:
  - Date
  - From
  - To
  - Subject
  - Message ID

- **Interactive Mode**: Allows multiple queries without restarting the script

- **Error Handling**: Proper error handling for invalid date formats and API issues

## Usage

### Interactive Mode (Recommended)

```bash
cd TABULA/tests
python3 test_gmail_selection.py
```

This will start an interactive session where you can:
- Enter different timestamps to test
- Specify custom labels
- Set maximum result limits
- Run multiple queries

### Command Line Mode

#### Test with DD/MM/YYYY format
```bash
python3 test_gmail_selection.py --timestamp "15/12/2024"
```

#### Test with Unix timestamp
```bash
python3 test_gmail_selection.py --timestamp "1734220800"
```

#### Test without timestamp filter (recent messages)
```bash
python3 test_gmail_selection.py --no-timestamp
```

#### Test with custom label and max results
```bash
python3 test_gmail_selection.py --timestamp "15/12/2024" --label "CUSTOM/LABEL" --max-results 100
```

#### Force interactive mode
```bash
python3 test_gmail_selection.py --interactive
```

## Parameters

- `--timestamp, -t`: Timestamp in DD/MM/YYYY format or Unix seconds
- `--label, -l`: Gmail label to search for (default: "02_S-ARGAME/LEADS")
- `--max-results, -m`: Maximum number of messages to retrieve (default: 50)
- `--no-timestamp`: Run without timestamp filter
- `--interactive, -i`: Run in interactive mode

## Output Format

The tool displays results in the following format:

```
================================================================================
GMAIL EMAIL SELECTION TEST
================================================================================
✅ Gmail service initialized successfully
📅 Using timestamp filter: 2024-12-15 00:00:00 (Unix: 1734220800)
🏷️  Searching label: 02_S-ARGAME/LEADS
📊 Maximum results: 50

--------------------------------------------------------------------------------
SEARCHING FOR MESSAGES...
--------------------------------------------------------------------------------
📬 Found 25 message(s)

--------------------------------------------------------------------------------
FETCHING MESSAGE DETAILS...
--------------------------------------------------------------------------------
📥 Fetching content for 25 messages...
✅ Successfully fetched 25 message(s)

--------------------------------------------------------------------------------
GROUPING MESSAGES BY THREAD...
--------------------------------------------------------------------------------
📊 Found 8 conversation thread(s)

================================================================================
GMAIL THREAD ANALYSIS RESULTS
================================================================================

🧵 THREAD 1: <EMAIL>
   Thread ID: 18c1234567890abcd
   Messages: 3

+---------------------+---------------------------+---------------------------+--------------------------------------------------+--------------------+
| Date                | From                      | To                        | Subject                                          | Message ID         |
+---------------------+---------------------------+---------------------------+--------------------------------------------------+--------------------+
| 2024-12-15 10:30:15 | <EMAIL>      | <EMAIL>     | Inquiry about S-ARGAME pricing                   | 18c1234567890ab... |
| 2024-12-15 11:45:22 | <EMAIL>     | <EMAIL>      | Re: Inquiry about S-ARGAME pricing              | 18c1234567890ac... |
| 2024-12-15 14:20:33 | <EMAIL>      | <EMAIL>     | Re: Inquiry about S-ARGAME pricing              | 18c1234567890ad... |
+---------------------+---------------------------+---------------------------+--------------------------------------------------+--------------------+

------------------------------------------------------------

🧵 THREAD 2: <EMAIL>
   Thread ID: 18c1234567890efgh
   Messages: 2

[Additional threads...]

================================================================================
SUMMARY
================================================================================
📊 Total threads analyzed: 8
📧 Total messages processed: 25
📅 Timestamp filter: 15/12/2024
🏷️  Label filter: 02_S-ARGAME/LEADS
📅 Message date range:
   Oldest: 2024-12-15 08:15:30
   Newest: 2024-12-16 16:45:12
```

## Technical Notes

### Timestamp Handling
- The tool properly handles the Gmail API timestamp format requirements
- Gmail API expects timestamps in seconds, but the codebase stores them in milliseconds
- The tool automatically converts between formats as needed

### Lead Identification
- Identifies lead emails by excluding company domains:
  - tabulatouch.com
  - tabulatouch.eu
- Shows "Unknown Lead" if no external email is found in the thread

### Error Handling
- Validates timestamp formats before making API calls
- Handles Gmail API errors gracefully
- Provides clear error messages for troubleshooting

## Integration with CRM System

This test tool uses the same underlying functions as the production CRM system:
- `gmail_process.get_email_messages()` for message retrieval
- `gmail_process.get_message_content()` for full message details
- `gmail_process.get_message_headers()` for header extraction
- Same label filtering as `sargame_CRM.py` (`--label "02_S-ARGAME/LEADS"`)

## Troubleshooting

### Common Issues

1. **"Failed to get Gmail service"**
   - Check that Google API credentials are properly configured
   - Ensure token.json is valid and not expired
   - Run `python3 ../tasks/check_token.py` to verify credentials

2. **"No messages found"**
   - Verify the label exists in Gmail
   - Check if the timestamp filter is too restrictive
   - Try running without timestamp filter first

3. **"Invalid date format"**
   - Use DD/MM/YYYY format (e.g., "15/12/2024")
   - Or use Unix timestamp in seconds
   - Check that the date is valid

### Debug Mode
For additional debugging, you can modify the script to enable more verbose output from the gmail_process utility.

## Examples

### Test Recent Messages
```bash
python3 test_gmail_selection.py --no-timestamp --max-results 10
```

### Test Specific Date Range
```bash
python3 test_gmail_selection.py --timestamp "01/12/2024"
```

### Interactive Testing Session
```bash
python3 test_gmail_selection.py
# Then enter various timestamps interactively
```

This tool provides a comprehensive way to test and visualize Gmail email selection functionality, making it easy to verify that the CRM system is correctly identifying and processing lead emails.
