#!/usr/bin/env python3
"""
Interactive test tool for Gmail email selection functionality.

This test tool allows you to query and visualize Gmail threads for leads using different timestamp inputs.
It uses the same parameters and configuration as found in sargame_crm.sh and tests the Gmail email
selection/filtering capabilities of the gmail_process.py utility.

Features:
- Accept timestamp parameter for Gmail queries
- Support two timestamp formats: Unix timestamp in seconds and DD/MM/YYYY
- Convert DD/MM/YYYY format to Unix seconds before passing to gmail_process.py
- Display results as multiple prettytables (one table per lead email found)
- Each table shows emails in a thread, ordered chronologically
- Include columns for: 'from', 'to', and other relevant email metadata
- Group emails by lead/thread for better organization
"""

import os
import sys
import datetime
import argparse
import re
from collections import defaultdict
from prettytable import PrettyTable

# Add parent directory to path to import utils
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from utils import gmail_process, google_credentials


def parse_date_input(date_input):
    """
    Parse date input in multiple formats and convert to Unix timestamp in seconds.

    Args:
        date_input (str): Date in format DD/MM/YYYY or Unix timestamp in seconds

    Returns:
        int: Unix timestamp in seconds

    Raises:
        ValueError: If date format is invalid
    """
    # Check if it's already a Unix timestamp (all digits)
    if date_input.isdigit():
        timestamp = int(date_input)
        # If it looks like milliseconds (13+ digits), convert to seconds
        if timestamp > 10000000000:  # Roughly year 2286 in seconds
            timestamp = timestamp // 1000
        return timestamp

    # Try to parse DD/MM/YYYY format
    try:
        # Parse DD/MM/YYYY format
        date_obj = datetime.datetime.strptime(date_input, "%d/%m/%Y")
        # Convert to Unix timestamp in seconds
        return int(date_obj.timestamp())
    except ValueError:
        pass

    # Try to parse YYYY-MM-DD format as well
    try:
        date_obj = datetime.datetime.strptime(date_input, "%Y-%m-%d")
        return int(date_obj.timestamp())
    except ValueError:
        pass

    raise ValueError(f"Invalid date format: {date_input}. Use DD/MM/YYYY, YYYY-MM-DD, or Unix timestamp in seconds.")


def format_timestamp(timestamp_ms):
    """
    Format a timestamp in milliseconds to a readable string.

    Args:
        timestamp_ms (int): Timestamp in milliseconds

    Returns:
        str: Formatted date string
    """
    try:
        # Check for obviously invalid timestamps
        if timestamp_ms < 0 or timestamp_ms > 9999999999999:  # Year 2286 in milliseconds
            return "Invalid timestamp"
        dt = datetime.datetime.fromtimestamp(timestamp_ms / 1000)
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except (ValueError, OSError):
        return "Invalid timestamp"


def extract_email_from_header(header_value):
    """
    Extract email address from a header value that might contain name and email.

    Args:
        header_value (str): Header value like "Name <<EMAIL>>" or "<EMAIL>"

    Returns:
        str: Clean email address
    """
    if not header_value:
        return ""

    # Look for email in angle brackets
    email_match = re.search(r'<([^>]+)>', header_value)
    if email_match:
        return email_match.group(1)

    # If no angle brackets, assume the whole thing is an email
    # Remove any extra whitespace
    return header_value.strip()


def group_messages_by_thread(messages):
    """
    Group messages by thread ID and sort them chronologically within each thread.

    Args:
        messages (list): List of full message objects

    Returns:
        dict: Dictionary with thread_id as key and list of messages as value
    """
    threads = defaultdict(list)

    for message in messages:
        thread_id = message.get('threadId', 'unknown')
        threads[thread_id].append(message)

    # Sort messages within each thread by timestamp
    for thread_id in threads:
        threads[thread_id].sort(key=lambda x: int(x.get('internalDate', 0)))

    return threads


def create_thread_table(thread_messages, thread_id):
    """
    Create a prettytable for a single thread of messages.

    Args:
        thread_messages (list): List of messages in the thread
        thread_id (str): Thread ID

    Returns:
        PrettyTable: Formatted table for the thread
    """
    table = PrettyTable()
    table.field_names = ["Date", "From", "To", "Subject", "Message ID"]
    table.align = "l"
    table.max_width["Subject"] = 50
    table.max_width["From"] = 30
    table.max_width["To"] = 30
    table.max_width["Message ID"] = 20

    for message in thread_messages:
        # Extract headers
        headers = gmail_process.get_message_headers(message)

        # Get timestamp and format it
        timestamp_ms = int(message.get('internalDate', 0))
        formatted_date = format_timestamp(timestamp_ms)

        # Extract email addresses
        from_email = extract_email_from_header(headers.get('from', ''))
        to_email = extract_email_from_header(headers.get('to', ''))

        # Get subject
        subject = headers.get('subject', '')
        if len(subject) > 47:  # Account for "..."
            subject = subject[:47] + "..."

        # Get message ID (shortened)
        msg_id = message.get('id', '')
        if len(msg_id) > 17:
            msg_id = msg_id[:17] + "..."

        table.add_row([
            formatted_date,
            from_email,
            to_email,
            subject,
            msg_id
        ])

    return table


def identify_lead_email(thread_messages, your_domains):
    """
    Identify the lead email address in a thread (non-company email).

    Args:
        thread_messages (list): List of messages in the thread
        your_domains (list): List of company domains

    Returns:
        str: Lead email address or "Unknown Lead"
    """
    lead_emails = set()

    for message in thread_messages:
        headers = gmail_process.get_message_headers(message)

        # Check both from and to headers
        for header_name in ['from', 'to']:
            email = extract_email_from_header(headers.get(header_name, ''))
            if email:
                # Check if this email is NOT from your company domains
                domain = email.split('@')[-1].lower() if '@' in email else ''
                if domain and domain not in [d.lower() for d in your_domains]:
                    lead_emails.add(email)

    # Return the first lead email found, or "Unknown Lead" if none found
    return list(lead_emails)[0] if lead_emails else "Unknown Lead"


def test_gmail_selection(timestamp_input=None, label="02_S-ARGAME/LEADS", max_results=50, sender=None, receiver=None):
    """
    Test Gmail email selection functionality with the specified parameters.

    Args:
        timestamp_input (str): Timestamp in DD/MM/YYYY format or Unix seconds
        label (str): Gmail label to search for
        max_results (int): Maximum number of messages to retrieve
        sender (str): Email address of the sender to filter by
        receiver (str): Email address of the receiver to filter by
    """
    print("=" * 80)
    print("GMAIL EMAIL SELECTION TEST")
    print("=" * 80)

    # Get Gmail API service
    service = google_credentials.get_gmail_service()
    if service is None:
        print("❌ Failed to get Gmail service. Check your credentials.")
        return

    print("✅ Gmail service initialized successfully")

    # Parse timestamp if provided
    after_timestamp = None
    if timestamp_input:
        try:
            timestamp_seconds = parse_date_input(timestamp_input)
            # gmail_process.py expects timestamps in milliseconds and will convert to seconds internally
            # So we need to convert our seconds to milliseconds
            after_timestamp = timestamp_seconds * 1000

            formatted_date = datetime.datetime.fromtimestamp(timestamp_seconds).strftime("%Y-%m-%d %H:%M:%S")
            print(f"📅 Using timestamp filter: {formatted_date} (Unix seconds: {timestamp_seconds})")
            print(f"📅 Passing to gmail_process: {after_timestamp} (milliseconds)")
        except ValueError as e:
            print(f"❌ Error parsing timestamp: {e}")
            return
    else:
        print("📅 No timestamp filter applied (will show recent messages)")

    print(f"🏷️  Searching label: {label}")
    print(f"📊 Maximum results: {max_results}")
    if sender:
        print(f"📧 Sender filter: {sender}")
    if receiver:
        print(f"📧 Receiver filter: {receiver}")

    print("\n" + "-" * 80)
    print("SEARCHING FOR MESSAGES...")
    print("-" * 80)

    # Get messages using gmail_process utility
    # Pass the timestamp in milliseconds as gmail_process.py expects it that way
    # It will internally convert milliseconds to seconds for the Gmail API

    try:
        message_ids = gmail_process.get_email_messages(
            service=service,
            sender=sender,
            subject=None,
            label=label,
            after_timestamp=after_timestamp,  # Pass timestamp in milliseconds as expected by gmail_process.py
            receiver=receiver
        )

        if not message_ids:
            print("📭 No messages found matching the criteria")
            return

        print(f"📬 Found {len(message_ids)} message(s)")

        # Limit results if requested
        if len(message_ids) > max_results:
            print(f"⚠️  Limiting to first {max_results} messages")
            message_ids = message_ids[:max_results]

    except Exception as e:
        print(f"❌ Error searching for messages: {e}")
        return

    print("\n" + "-" * 80)
    print("FETCHING MESSAGE DETAILS...")
    print("-" * 80)

    # Fetch full message content for all IDs
    full_messages = []
    print(f"📥 Fetching content for {len(message_ids)} messages...")

    for i, msg_ref in enumerate(message_ids, 1):
        try:
            full_msg = gmail_process.get_message_content(service, msg_ref['id'])
            if full_msg:
                full_messages.append(full_msg)
                if i % 10 == 0:  # Progress indicator every 10 messages
                    print(f"   📥 Fetched {i}/{len(message_ids)} messages...")
            else:
                print(f"⚠️  Skipping message {msg_ref['id']} due to fetch error.")
        except Exception as e:
            print(f"⚠️  Error fetching message {msg_ref['id']}: {e}")

    if not full_messages:
        print("❌ No message content could be retrieved")
        return

    print(f"✅ Successfully fetched {len(full_messages)} message(s)")

    print("\n" + "-" * 80)
    print("GROUPING MESSAGES BY THREAD...")
    print("-" * 80)

    # Group messages by thread
    threads = group_messages_by_thread(full_messages)
    print(f"📊 Found {len(threads)} conversation thread(s)")

    # Define company domains (same as in sargame_CRM.py)
    your_domains = [
        "tabulatouch.com",
        "tabulatouch.eu",
    ]

    print("\n" + "=" * 80)
    print("GMAIL THREAD ANALYSIS RESULTS")
    print("=" * 80)

    # Process each thread and create tables
    thread_count = 0
    for thread_id, thread_messages in threads.items():
        thread_count += 1

        # Identify the lead email for this thread
        lead_email = identify_lead_email(thread_messages, your_domains)

        print(f"\n🧵 THREAD {thread_count}: {lead_email}")
        print(f"   Thread ID: {thread_id}")
        print(f"   Messages: {len(thread_messages)}")

        # Create and display the table for this thread
        table = create_thread_table(thread_messages, thread_id)
        print(table)

        # Add some spacing between threads
        if thread_count < len(threads):
            print("\n" + "-" * 60)

    print("\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)
    print(f"📊 Total threads analyzed: {len(threads)}")
    print(f"📧 Total messages processed: {len(full_messages)}")

    if timestamp_input:
        print(f"📅 Timestamp filter: {timestamp_input}")
    print(f"🏷️  Label filter: {label}")
    if sender:
        print(f"📧 Sender filter: {sender}")
    if receiver:
        print(f"📧 Receiver filter: {receiver}")

    # Show timestamp range of messages
    if full_messages:
        timestamps = [int(msg.get('internalDate', 0)) for msg in full_messages]
        oldest = min(timestamps)
        newest = max(timestamps)

        print(f"📅 Message date range:")
        print(f"   Oldest: {format_timestamp(oldest)}")
        print(f"   Newest: {format_timestamp(newest)}")


def interactive_mode():
    """
    Run the test tool in interactive mode, allowing multiple queries.
    """
    print("🔄 INTERACTIVE GMAIL SELECTION TEST MODE")
    print("=" * 80)
    print("Enter timestamps to test Gmail email selection.")
    print("Supported formats:")
    print("  - DD/MM/YYYY (e.g., 15/12/2024)")
    print("  - YYYY-MM-DD (e.g., 2024-12-15)")
    print("  - Unix timestamp in seconds (e.g., 1734220800)")
    print("  - Press Enter for no timestamp filter")
    print("  - Type 'quit' or 'exit' to stop")
    print("-" * 80)

    while True:
        try:
            # Get timestamp input
            timestamp_input = input("\n📅 Enter timestamp (or 'quit'): ").strip()

            if timestamp_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break

            # Get optional parameters
            label_input = input(f"🏷️  Enter label []: ").strip()
            label = label_input # if label_input else "02_S-ARGAME/LEADS"

            sender_input = input(f"📧 Enter sender email (optional): ").strip()
            sender = sender_input if sender_input else None

            receiver_input = input(f"📧 Enter receiver email (optional): ").strip()
            receiver = receiver_input if receiver_input else None

            max_results_input = input(f"📊 Max results [50]: ").strip()
            try:
                max_results = int(max_results_input) if max_results_input else 50
            except ValueError:
                max_results = 50

            # Run the test
            timestamp_to_use = timestamp_input if timestamp_input else None
            test_gmail_selection(timestamp_to_use, label, max_results, sender, receiver)

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


def main():
    """
    Main function to handle command line arguments and run the test.
    """
    parser = argparse.ArgumentParser(
        description="Test Gmail email selection functionality",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Interactive mode
  python test_gmail_selection.py

  # Test with DD/MM/YYYY format
  python test_gmail_selection.py --timestamp "15/12/2024"

  # Test with Unix timestamp
  python test_gmail_selection.py --timestamp "1734220800"

  # Test with custom label and max results
  python test_gmail_selection.py --timestamp "15/12/2024" --label "CUSTOM/LABEL" --max-results 100

  # Test with sender filter
  python test_gmail_selection.py --sender "<EMAIL>"

  # Test with receiver filter
  python test_gmail_selection.py --receiver "<EMAIL>"

  # Test with both sender and receiver filters
  python test_gmail_selection.py --sender "<EMAIL>" --receiver "<EMAIL>"

  # Test without timestamp filter
  python test_gmail_selection.py --no-timestamp
        """
    )

    parser.add_argument(
        '--timestamp', '-t',
        help='Timestamp in DD/MM/YYYY format or Unix seconds'
    )

    parser.add_argument(
        '--label', '-l',
        default="02_S-ARGAME/LEADS",
        help='Gmail label to search for (default: 02_S-ARGAME/LEADS)'
    )

    parser.add_argument(
        '--sender', '-s',
        help='Email address of the sender to filter by'
    )

    parser.add_argument(
        '--receiver', '-r',
        help='Email address of the receiver to filter by'
    )

    parser.add_argument(
        '--max-results', '-m',
        type=int,
        default=50,
        help='Maximum number of messages to retrieve (default: 50)'
    )

    parser.add_argument(
        '--no-timestamp',
        action='store_true',
        help='Run without timestamp filter (show recent messages)'
    )

    parser.add_argument(
        '--interactive', '-i',
        action='store_true',
        help='Run in interactive mode for multiple queries'
    )

    args = parser.parse_args()

    # Run in interactive mode if requested or no arguments provided
    if args.interactive or (not args.timestamp and not args.no_timestamp and not args.sender and not args.receiver):
        interactive_mode()
    else:
        # Run single test
        timestamp_to_use = args.timestamp if not args.no_timestamp else None
        test_gmail_selection(timestamp_to_use, args.label, args.max_results, args.sender, args.receiver)


if __name__ == "__main__":
    main()
