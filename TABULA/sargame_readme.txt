Python Virtual environment
    python3 -m venv .venv
    source .venv/bin/activate
    python3 -m pip install -r requirements.txt

Google Auth
Google credentials file must be downloaded to utils/client_secret.json
The authenticated token.json will be saved in the /token  folder.


Servers to start
- ollama run gemma2:2b
- waha docker
    - (if not created already) here is the docker command:  "docker run -d -p 3000:3000 -v waha_sessions_data:/app/.sessions --name waha sha256:750157a15944f179e453251fbedf536141317098fd801af6470f10e89af65edb"
    - start with: "docker start waha"
    - go to http://localhost:3000/dashboard
    - it should persist everything the phone pairingd, but  you need to start the session
    - webhook is not persisted, in config put messages and messages.any webhook to http://host.docker.internal:50000/waha-webhook
- screen -t WAHA
- sargame_waha_wh_run.sh (our webhook receiver that logs whatsapp activity to whatsapp_numbers.txt)
- screen -t SARGAME
- sargame_crm_run.sh (that runs a 1 hour check of the crm heartbeat)
