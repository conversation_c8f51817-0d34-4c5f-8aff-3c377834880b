# WhatsApp Webhook Duplicate Filtering

## Problem

When using WAHA (WhatsApp HTTP API) with both `message` and `message.any` webhook events enabled, the same WhatsApp message triggers both events, causing duplicate entries in the database.

## Solution

The filtering system implemented in `utils/waha.py` prevents duplicates by:

1. **Processing `message` events only for incoming messages** (`fromMe: false`)
2. **Processing `message.any` events only for outgoing messages** (`fromMe: true`)
3. **Filtering out the duplicate event** while preserving all message data

## Configuration

### In `utils/CONFIG.py`:

```python
# Your WhatsApp phone number (for improved filtering)
MY_WHATSAPP_NUMBER = "+393338208586"

# Enable/disable duplicate filtering
WHATSAPP_FILTER_DUPLICATE_EVENTS = True
```

### Webhook Events Setup

In your WAHA dashboard, configure webhooks to send both events to:
```
http://host.docker.internal:50000/waha-webhook
```

Events to enable:
- `message` (for incoming messages)
- `message.any` (for outgoing messages)

## How It Works

### Event Flow

1. **Incoming Message** (from contact to you):
   - WA<PERSON> sends both `message` and `message.any` events
   - Filter processes `message` event ✅
   - Filter skips `message.any` event ❌ (prevents duplicate)

2. **Outgoing Message** (from you to contact):
   - WAHA sends both `message` and `message.any` events  
   - Filter skips `message` event ❌ (prevents duplicate)
   - Filter processes `message.any` event ✅

### Filtering Logic

```python
def should_process_webhook_event(event_type, message_data):
    payload = message_data.get('payload', {})
    from_me = payload.get('fromMe', False)
    
    if event_type == 'message':
        return not from_me  # Process incoming only
    elif event_type == 'message.any':
        return from_me      # Process outgoing only
    else:
        return True         # Process all other events
```

## Benefits

- ✅ **Eliminates duplicate database entries**
- ✅ **Preserves all message data** (both incoming and outgoing)
- ✅ **Maintains existing functionality**
- ✅ **Configurable** (can be disabled if needed)
- ✅ **Backward compatible**

## Testing

Run the test script to verify filtering works correctly:

```bash
cd TABULA
python test_webhook_filtering.py
```

## Disabling Filtering

To disable filtering (not recommended), set in `CONFIG.py`:

```python
WHATSAPP_FILTER_DUPLICATE_EVENTS = False
```

## Logs

When filtering is active, you'll see logs like:

```
✓ Duplicate event filtering is ENABLED (prevents 'message' and 'message.any' duplicates)
Skipping 'message.any' event for incoming message (preventing duplicate with 'message')
Skipping 'message' event for outgoing message (preventing duplicate with 'message.any')
```

## Database Impact

- **Before**: Same message stored twice (once from each event)
- **After**: Each message stored once (from the appropriate event)
- **Lead filtering**: Only messages from/to leads are stored (unchanged)
- **Duplicate detection**: Existing duplicate detection still works as backup
