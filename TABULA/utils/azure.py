#!/usr/bin/env python3
# filepath: /Users/<USER>/GIT/tabula.scripts/TABULA/utils/azure.py

"""
Azure utilities for Tabula scripts, including database connection functions.
"""

import pyodbc
import sys
from datetime import datetime, timedelta

def connect_sql_fcd():
    """
    Connect to Azure SQL database (facade design) using a connection string.
    Returns a connection object if successful.
    """
    connection_string = (
        "Driver={ODBC Driver 17 for SQL Server};"
        "Server=tcp:gipjdfq28f.database.windows.net,1433;"
        "Database=TabulaTouchDB1;"
        "Uid=tabuladb1user@gipjdfq28f;"
        "Pwd=2AknxgO0ZLa8vs1mXEOxOi;"
        "Encrypt=yes;"
        "TrustServerCertificate=no;"
        "Connection Timeout=160;"
    )
    
    try:
        conn = pyodbc.connect(connection_string)
        return conn
    except pyodbc.Error as e:
        print(f"Error connecting to Azure SQL database: {e}")
        sys.exit(1)

def get_fcd_download_emails(days=30):
    """
    Retrieve emails from the DownloadLog table for the last specified number of days.
    
    Args:
        days: Number of days to look back (default: 30)
        
    Returns:
        List of unique email addresses
    """
    conn = connect_sql_fcd()
    try:
        cursor = conn.cursor()
        
        # Calculate the date from specified days ago
        past_date = datetime.now() - timedelta(days=days)
        
        # Format the date for SQL query
        date_string = past_date.strftime('%Y-%m-%d')
        
        # Query to get emails from the specified period
        query = f"""
        SELECT DISTINCT Email 
        FROM DownloadLog 
        WHERE Email IS NOT NULL 
        AND Email <> '' 
        AND DateTime >= '{date_string}'
        ORDER BY Email
        """
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        emails = [row.Email for row in rows]
        return emails
    
    except pyodbc.Error as e:
        print(f"Error executing query: {e}")
        return []
    finally:
        cursor.close()
        conn.close()
