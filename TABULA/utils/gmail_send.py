import os
import re
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from googleapiclient.errors import HttpError
import base64

# Import from the same utils directory
from . import google_credentials

def create_mail(sender_email, recipient_email, subject, message_body, is_html=True, label=None, draft=True):
    """Creates a draft email or sends it directly in Gmail.

    Args:
        sender_email: The email address to send the email from (must be an alias in Gmail).
        recipient_email: The email address to send the email to.
        subject: The subject of the email.
        message_body: The body of the email.
        credentials_file: Path to the JSON file containing Gmail API credentials.
        is_html: Whether the message_body contains HTML (default: True).
        label: Label to apply to the draft (optional).
        draft: Whether to create a draft (True) or send the email directly (False).

    Returns:
        The ID of the created draft/message, or None if an error occurred.
    """
    try:
        # Check if we should disable auth flow based on environment variable
        disable_auth_flow = os.environ.get("DISABLE_AUTH_FLOW", "0") == "1"

        # Get Gmail API service using the utility module
        service = google_credentials.get_gmail_service(authflow=not disable_auth_flow)

        if service is None:
            print("Failed to obtain Gmail service.")
            return None

        # If we have a complete email (with headers), parse it and use it directly
        if message_body.startswith('MIME-Version:') or message_body.startswith('From:') or message_body.startswith('Content-Type:'):
            # Parse the email message - ensure proper handling of UTF-8
            msg = email.message_from_string(message_body)

            # Override headers with the provided values
            if subject:
                msg.replace_header('Subject', subject)
            msg.replace_header('To', recipient_email)
            msg.replace_header('From', sender_email)

            # Ensure proper encoding of the message
            raw_message = base64.urlsafe_b64encode(msg.as_bytes()).decode()
        else:
            # Create a new message
            message = MIMEMultipart('alternative')
            message['to'] = recipient_email
            message['from'] = sender_email
            message['subject'] = subject

            # Attach text parts with explicit UTF-8 encoding
            if is_html:
                message.attach(MIMEText(message_body, 'html', 'utf-8'))
                # Create a plain text version for compatibility
                plain_text = re.sub('<[^<]+?>', '', message_body)  # Simple HTML tag stripping
                message.attach(MIMEText(plain_text, 'plain', 'utf-8'))
            else:
                message.attach(MIMEText(message_body, 'plain', 'utf-8'))

            # Encode the message
            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()

        message_id = None

        # Either create a draft or send the email directly
        if draft:
            # Creating the draft
            create_message = {'message': {'raw': raw_message}}
            draft = service.users().drafts().create(userId="me", body=create_message).execute()
            message_id = draft['id']
            print(f"Draft id: {message_id}")

            # Get the message ID from the draft
            draft_info = service.users().drafts().get(userId='me', id=message_id).execute()
            message_id = draft_info.get('message', {}).get('id')
        else:
            # Send the email directly
            create_message = {'raw': raw_message}
            message = service.users().messages().send(userId="me", body=create_message).execute()
            message_id = message['id']
            print(f"Message sent, id: {message_id}")

        # Apply label if specified
        if label and message_id:
            try:
                # First, we need to find the label ID for the given label name
                results = service.users().labels().list(userId='me').execute()
                labels = results.get('labels', [])

                label_id = None
                for label_obj in labels:
                    if label_obj['name'] == label:
                        label_id = label_obj['id']
                        break

                if label_id:
                    # Apply the label to the message
                    service.users().messages().modify(
                        userId='me',
                        id=message_id,
                        body={'addLabelIds': [label_id]}
                    ).execute()
                    print(f"Label '{label}' applied to {'draft' if draft else 'message'}.")
                else:
                    print(f"Warning: Label '{label}' not found.")
            except Exception as e:
                print(f"Error applying label: {e}")

        return message_id

    except HttpError as error:
        print(f"An error occurred: {error}")
        return None
    except Exception as e:
        print(f"A general error occurred: {e}")
        return None


def create_html_body(sender_email, recipient_email, subject, html_body, plain_text=None):
    """Creates a properly formatted MIME HTML email body.

    Args:
        sender_email (str): The email address to send the email from.
        recipient_email (str): The email address to send the email to.
        subject (str): The subject of the email.
        html_body (str): The HTML content of the email.
        plain_text (str, optional): Plain text alternative. If not provided,
                                   will be generated by stripping HTML tags.

    Returns:
        str: A complete MIME-formatted email message ready to be passed to create_mail().
    """
    # Generate plain text version if not provided
    if plain_text is None:
        plain_text = re.sub('<[^<]+?>', '', html_body)  # Simple HTML tag stripping

    # Create a proper MIME message using email.mime classes
    from email.mime.multipart import MIMEMultipart
    from email.mime.text import MIMEText

    # Create the main message container
    message = MIMEMultipart('alternative')
    message['Subject'] = subject
    message['From'] = sender_email
    message['To'] = recipient_email

    # Attach the plain text and HTML parts
    part1 = MIMEText(plain_text, 'plain', 'utf-8')
    part2 = MIMEText(html_body, 'html', 'utf-8')

    # The email client will try to render the last part first
    message.attach(part1)
    message.attach(part2)

    # Convert the MIMEMultipart object to a string
    return message.as_string()


def load_template(template_name, templates_dir="templates"):
    """Loads a template from a file.

    Args:
        template_name: The name of the template (without extension) or an absolute path to a template file.
        templates_dir: The directory where the templates are stored (used if template_name is not an absolute path).

    Returns:
        The content of the template as a string, or None if the file doesn't exist.
    """
    # Check if template_name is already a full path
    if os.path.isabs(template_name):
        template_path = template_name
    else:
        template_path = os.path.join(templates_dir, f"{template_name}.txt")

    try:
        with open(template_path, "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        print(f"Template file not found: {template_path}")
        return None