

SMTP_IN="mail.s-argame.com"
SMTP_IN_PORT=993

SMTP_OUT="mail.s-argame.com"
SMTP_OUT_PORT=465

SMTP_USER="<EMAIL>"
SMTP_PASSWORD="s_f1QbmGf@_+"


def send_mail(sender_email, recipient_email, subject, message_body, is_html=True):
    """
    Send an email using SMTP.
    
    Args:
        sender_email (str): Email address of the sender
        recipient_email (str): Email address of the recipient
        subject (str): Subject of the email
        message_body (str): Body content of the email
        is_html (bool): If True, message is treated as HTML, otherwise as plain text
        
    Returns:
        dict: Result of the operation with status and message
    """
    import smtplib
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart
    from email.header import Header
    
    try:
        # Create message container
        msg = MIMEMultipart('alternative')
        msg['Subject'] = Header(subject, 'utf-8')
        msg['From'] = sender_email
        msg['To'] = recipient_email
        
        # Determine content type based on is_html flag
        content_type = 'html' if is_html else 'plain'
        
        # Attach the message body with appropriate content type
        part = MIMEText(message_body, content_type, 'utf-8')
        msg.attach(part)
        
        # Connect to SMTP server
        with smtplib.SMTP_SSL(SMTP_OUT, SMTP_OUT_PORT) as server:
            # Login to the server
            server.login(SMTP_USER, SMTP_PASSWORD)
            
            # Send email
            server.sendmail(
                sender_email,
                recipient_email,
                msg.as_string()
            )
            
        return {
            'status': 'success',
            'message': f'Email sent successfully to {recipient_email}'
        }
        
    except Exception as e:
        return {
            'status': 'error',
            'message': f'Failed to send email: {str(e)}'
        }