import json
import os
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow


def get_google_credentials(credentials_file="../utils/client_secret.json", token_file="../token/token.json", scopes=None, authflow=True):
    """
    Gets Google API credentials.
    Uses saved credentials if available, otherwise runs OAuth flow if authflow is True.
    
    Args:
        credentials_file: Path to the JSON file containing API credentials.
        token_file: Path to save/load the token.
        scopes: List of API scopes to request.
        authflow: Boolean indicating whether to start the OAuth flow if token is invalid (default: True)
        
    Returns:
        tuple: (credentials object, service object if created successfully or None)
    """
    if scopes is None:
        scopes = [ 'https://www.googleapis.com/auth/gmail.readonly',
                 'https://www.googleapis.com/auth/gmail.compose',
                 'https://www.googleapis.com/auth/gmail.labels',
                 'https://www.googleapis.com/auth/gmail.modify',
                 'https://www.googleapis.com/auth/spreadsheets']
    
    
    creds = None
    # Load credentials from file if it exists
    if os.path.exists(token_file):
        try:
            with open(token_file, 'r') as f:
                creds_data = json.load(f)
            creds = Credentials.from_authorized_user_info(creds_data)
        except Exception as e:
            print(f"Error loading credentials from file: {e}")
            creds = None  # Ensure creds is None if loading fails

    # If there are no (valid) credentials available, try to refresh or run the OAuth flow.
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            try:
                creds.refresh(Request())
            except Exception as e:
                print(f"Error refreshing credentials: {e}")
                creds = None  # Ensure creds is None if refresh fails
        elif authflow:
            # Run the OAuth 2.0 flow to get new credentials only if authflow is enabled
            try:
                flow = InstalledAppFlow.from_client_secrets_file(
                    credentials_file, scopes)
                creds = flow.run_local_server(port=0) # Auto-select an unused port
            except Exception as e:
                print(f"Error in OAuth flow: {e}")
                return None
        else:
            # If authflow is disabled, just return None for invalid credentials
            print("Token is invalid and OAuth flow is disabled.")
            return None

        # Save the credentials for the next run
        if creds and creds.token:  # Only save if we successfully got credentials
            try:
                with open(token_file, 'w') as f:
                    json.dump({
                        'token': creds.token,
                        'refresh_token': creds.refresh_token,
                        'token_uri': creds.token_uri,
                        'client_id': creds.client_id,
                        'client_secret': creds.client_secret,
                        'scopes': creds.scopes
                    }, f)
                # print("Credentials saved to file.")
            except Exception as e:
                print(f"Error saving credentials: {e}")
        else:
            print("Failed to obtain credentials.")
            return None
    
    return creds


def get_gmail_service(credentials_file="../utils/client_secret.json", token_file="../token/token.json", scopes=None, authflow=True):
    """
    Authenticates and returns the Gmail API service.
    
    Args:
        credentials_file: Path to the JSON file containing API credentials.
        token_file: Path to save/load the token.
        scopes: List of API scopes to request.
        authflow: Boolean indicating whether to start the OAuth flow if token is invalid (default: True)
        
    Returns:
        service: The Gmail API service object or None if authentication failed
    """
    creds = get_google_credentials(credentials_file, token_file, scopes, authflow)
    
    if not creds:
        return None
        
    try:
        # Create and return Gmail API service
        service = build('gmail', 'v1', credentials=creds)
        return service
    except Exception as e:
        print(f"Error creating Gmail service: {e}")
        return None