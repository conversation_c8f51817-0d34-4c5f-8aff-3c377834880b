import requests
import json
import csv
import os
import datetime
import hashlib
import hmac
from pathlib import Path
from flask import Flask, request, jsonify
import sqlite3

# Import configuration
try:
    from .CONFIG import MY_WHATSAPP_NUMBER, WHATSAPP_FILTER_DUPLICATE_EVENTS
except ImportError:
    # Fallback if CONFIG is not available
    MY_WHATSAPP_NUMBER = None
    WHATSAPP_FILTER_DUPLICATE_EVENTS = True

# SQLite database path
SQLITE_DB_PATH = '../sqlite/crm_latest.sqlite'

def send_whatsapp_message(phone_number, message):

    phone_number_without_plus = phone_number.lstrip("+")

    url = "http://localhost:3000/api/sendText"
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json"
    }
    data = {
        "chatId": f"{phone_number_without_plus}@c.us",
        "text": message,
        "session": "default"
    }

    response = requests.post(url, json=data, headers=headers, timeout=30)
    return response.json()

def receive_whatsapp_message(message_data):
    """
    Process incoming WhatsApp messages, store phone numbers in a text file,
    and save message data to SQLite database.

    Args:
        message_data (dict): The message data received from Waha webhook.

    Returns:
        bool: True if message was processed successfully, False otherwise.
    """
    try:
        # Check if this is a message event with expected structure
        if 'payload' not in message_data:
            print(f"Unexpected message format: {message_data}")
            return False

        payload = message_data['payload']
        from_number = payload.get('from', 'unknown')
        to_number = payload.get('to', 'unknown')
        from_me = payload.get('fromMe', False)

        # Skip if both numbers are unknown or empty
        if (not from_number or from_number == 'unknown') and (not to_number or to_number == 'unknown'):
            return False

        # Determine which number to check and log based on message direction
        if from_me:
            # We sent the message - check the recipient (to_number)
            relevant_number = to_number
            direction = "sent to"
        else:
            # We received the message - check the sender (from_number)
            relevant_number = from_number
            direction = "received from"

        # Skip if relevant number is unknown or empty
        if not relevant_number or relevant_number == 'unknown':
            return False

        # Format the phone number correctly
        # Remove the "@c.us" or "@g.us" suffix
        if '@' in relevant_number:
            relevant_number = relevant_number.split('@')[0]

        # Add the '+' prefix if not present
        if not relevant_number.startswith('+'):
            relevant_number = '+' + relevant_number

        # Add the "p:" prefix for database consistency with CRM table
        db_phone_number = f"p:{relevant_number}"

        # Create logs directory if it doesn't exist (maintain existing functionality)
        logs_dir = Path(os.path.dirname(os.path.abspath(__file__))).parent / 'logs'
        logs_dir.mkdir(exist_ok=True)

        # Define text file path (maintain existing functionality)
        txt_file = logs_dir / 'whatsapp_numbers.txt'

        # Read existing numbers to avoid duplicates (maintain existing functionality)
        existing_numbers = set()
        if txt_file.exists():
            with open(txt_file, 'r', encoding='utf-8') as f:
                existing_numbers = {line.strip() for line in f}

        # Only write the number if it's not already in the file (maintain existing functionality)
        if relevant_number not in existing_numbers:
            with open(txt_file, 'a', encoding='utf-8') as f:
                f.write(f"{relevant_number}\n")
            print(f"Phone number {relevant_number} saved to text file")

        # Extract message data for database storage
        message_text = payload.get('body', '')
        timestamp = payload.get('timestamp', datetime.datetime.now().timestamp())

        # Convert timestamp to datetime
        message_date = datetime.datetime.fromtimestamp(timestamp)

        # Only store message in database if the phone number belongs to a lead
        if is_lead_phone_number(db_phone_number):
            stored = store_message_in_db(db_phone_number, message_date, from_me, message_text)
            if stored:
                if from_me:
                    print(f"Message sent to lead {relevant_number} stored in database")
                else:
                    print(f"Message received from lead {relevant_number} stored in database")
            # If not stored, the store_message_in_db function already printed the duplicate message
        else:
            if from_me:
                print(f"Message sent to non-lead {relevant_number} logged but not stored in database")
            else:
                print(f"Message received from non-lead {relevant_number} logged but not stored in database")

        return True

    except Exception as e:
        print(f"Error processing WhatsApp message: {str(e)}")
        return False

def should_process_webhook_event(event_type, message_data):
    """
    Determine if a webhook event should be processed to prevent duplicates.

    When both 'message' and 'message.any' events are enabled, this function
    filters them to prevent duplicate message storage:
    - 'message' events: Process only for incoming messages (fromMe: false)
    - 'message.any' events: Process only for outgoing messages (fromMe: true)

    Args:
        event_type (str): The webhook event type ('message' or 'message.any')
        message_data (dict): The webhook message data

    Returns:
        bool: True if the event should be processed, False if it should be skipped
    """
    # If filtering is disabled, process all events
    if not WHATSAPP_FILTER_DUPLICATE_EVENTS:
        return True

    # If it's not a message event, process it
    if event_type not in ['message', 'message.any']:
        return True

    # Extract fromMe flag from the payload
    payload = message_data.get('payload', {})
    from_me = payload.get('fromMe', False)

    # Apply filtering logic:
    # - 'message' events: only for incoming messages (fromMe: false)
    # - 'message.any' events: only for outgoing messages (fromMe: true)
    if event_type == 'message':
        should_process = not from_me  # Process incoming messages only
        if not should_process:
            print(f"Skipping 'message' event for outgoing message (preventing duplicate with 'message.any')")
    elif event_type == 'message.any':
        should_process = from_me  # Process outgoing messages only
        if not should_process:
            print(f"Skipping 'message.any' event for incoming message (preventing duplicate with 'message')")
    else:
        should_process = True

    return should_process

def verify_hmac_signature(request_data, signature, secret_key, algorithm='sha512'):
    """
    Verify HMAC signature from Waha webhook

    Args:
        request_data (bytes): The raw request body data
        signature (str): The signature from the X-Webhook-Hmac header
        secret_key (str): The secret key used to generate the signature
        algorithm (str): The hash algorithm used (default: sha512)

    Returns:
        bool: True if signature is valid, False otherwise
    """
    if not signature or not secret_key:
        return False

    # Compute HMAC with the secret key
    computed_hmac = hmac.new(
        secret_key.encode('utf-8'),
        request_data,
        getattr(hashlib, algorithm)
    ).hexdigest()

    # Compare computed HMAC with received signature
    return hmac.compare_digest(computed_hmac, signature)

def create_webhook_server(host='0.0.0.0', port=5000, endpoint='/waha-webhook',
                        hmac_secret_key=None, allowed_events=None):
    """
    Create a Flask server to handle Waha webhooks

    Args:
        host (str): The hostname to bind the server to
        port (int): The port to bind the server to
        endpoint (str): The endpoint path for the webhook
        hmac_secret_key (str, optional): Secret key for verifying HMAC signatures
        allowed_events (list, optional): List of event types to process

    Returns:
        Flask: The Flask application instance
    """
    app = Flask("WahaWebhookServer")

    # Default to processing message events if none specified
    if allowed_events is None:
        allowed_events = ['message', 'message.any']

    @app.route(endpoint, methods=['POST'])
    def webhook_handler():
        try:
            # Get the raw request data for HMAC verification
            request_data = request.get_data()

            # Verify HMAC signature if secret key is provided
            if hmac_secret_key:
                signature = request.headers.get('X-Webhook-Hmac')
                algorithm = request.headers.get('X-Webhook-Hmac-Algorithm', 'sha512')

                if not verify_hmac_signature(request_data, signature, hmac_secret_key, algorithm):
                    return jsonify({'status': 'error', 'message': 'Invalid signature'}), 403

            # Parse the JSON data
            message_data = request.json

            # Check if we should process this event
            event_type = message_data.get('event')
            if not event_type or (event_type not in allowed_events and '*' not in allowed_events):
                return jsonify({'status': 'ignored', 'event': event_type}), 200

            # Process message data
            if event_type in ['message', 'message.any']:
                # Check if we should process this specific event to prevent duplicates
                if should_process_webhook_event(event_type, message_data):
                    result = receive_whatsapp_message(message_data)
                    return jsonify({'status': 'success', 'processed': result}), 200
                else:
                    # Event was filtered out to prevent duplicate processing
                    return jsonify({'status': 'filtered', 'event': event_type, 'reason': 'duplicate_prevention'}), 200
            else:
                # Handle other event types here if needed
                return jsonify({'status': 'success', 'event': event_type, 'processed': False}), 200

        except Exception as e:
            print(f"Error handling webhook request: {str(e)}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    return app

def start_webhook_server(host='0.0.0.0', port=50000, endpoint='/waha-webhook',
                       hmac_secret_key=None, allowed_events=None, debug=False):
    """
    Start the Flask server for handling Waha webhooks

    Args:
        host (str): The hostname to bind the server to
        port (int): The port to bind the server to
        endpoint (str): The endpoint path for the webhook
        hmac_secret_key (str, optional): Secret key for verifying HMAC signatures
        allowed_events (list, optional): List of event types to process
        debug (bool): Whether to run Flask in debug mode

    Returns:
        None
    """
    app = create_webhook_server(host, port, endpoint, hmac_secret_key, allowed_events)
    print(f"Starting WhatsApp webhook server on http://{host}:{port}{endpoint}")
    print(f"Listening for events: {', '.join(allowed_events if allowed_events else ['message', 'message.any'])}")

    # Show filtering status
    if WHATSAPP_FILTER_DUPLICATE_EVENTS:
        print("✓ Duplicate event filtering is ENABLED (prevents 'message' and 'message.any' duplicates)")
    else:
        print("⚠ Duplicate event filtering is DISABLED (may cause duplicate message storage)")

    # Run the Flask app (blocking call)
    app.run(host=host, port=port, debug=debug)

def is_lead_phone_number(phone_number):
    """
    Check if a phone number belongs to a lead in the CRM database.

    Args:
        phone_number (str): The phone number with "p:" prefix to check

    Returns:
        bool: True if the phone number belongs to a lead, False otherwise
    """
    try:
        # Connect to SQLite database
        conn = sqlite3.connect(SQLITE_DB_PATH)
        cursor = conn.cursor()

        # Query the crm table to check if this phone number exists
        cursor.execute(
            "SELECT COUNT(*) FROM crm WHERE phone_number = ?",
            (phone_number,)
        )

        count = cursor.fetchone()[0]

        # Close connection
        conn.close()

        return count > 0

    except Exception as e:
        print(f"Error checking if phone number is a lead: {str(e)}")
        return False

def store_message_in_db(phone_number, date, from_me, message):
    """
    Store WhatsApp message in SQLite database.
    Checks for duplicates before inserting to avoid storing the same message twice.

    Args:
        phone_number (str): The phone number with "p:" prefix
        date (datetime): Message timestamp
        from_me (bool): Whether the message was sent by the system
        message (str): The message content

    Returns:
        bool: True if message was stored successfully, False if duplicate or error
    """
    try:
        # Connect to SQLite database
        conn = sqlite3.connect(SQLITE_DB_PATH)
        cursor = conn.cursor()

        # Check if a message with the same phone_number, date, from_me, and message already exists
        cursor.execute(
            "SELECT COUNT(*) FROM wa_messages WHERE phone_number = ? AND date = ? AND from_me = ? AND message = ?",
            (phone_number, date, from_me, message)
        )

        existing_count = cursor.fetchone()[0]

        if existing_count > 0:
            # Message already exists, don't insert duplicate
            conn.close()
            print(f"Duplicate message from {phone_number} at {date} - skipping")
            return False

        # Insert message in wa_messages table
        cursor.execute(
            "INSERT INTO wa_messages (phone_number, date, from_me, message) VALUES (?, ?, ?, ?)",
            (phone_number, date, from_me, message)
        )

        # Commit changes and close connection
        conn.commit()
        conn.close()

        print(f"Message from lead {phone_number} stored in database")
        return True

    except Exception as e:
        print(f"Error storing message in database: {str(e)}")
        return False

def get_message_thread(phone_number):
    """
    Retrieve the message thread for a specific phone number.

    Args:
        phone_number (str): The phone number to retrieve messages for.
                           Can be with or without "p:" prefix.

    Returns:
        list: A list of message dictionaries sorted by date.
    """
    try:
        # Format phone number if needed
        if not phone_number.startswith("p:"):
            if not phone_number.startswith("+"):
                phone_number = "+" + phone_number
            phone_number = f"p:{phone_number}"

        # Connect to SQLite database
        conn = sqlite3.connect(SQLITE_DB_PATH)
        conn.row_factory = sqlite3.Row  # This enables column access by name
        cursor = conn.cursor()

        # Query messages for the phone number, ordered by date
        cursor.execute(
            "SELECT * FROM wa_messages WHERE phone_number = ? ORDER BY date ASC",
            (phone_number,)
        )

        # Convert rows to dictionaries
        messages = [dict(row) for row in cursor.fetchall()]

        # Close connection
        conn.close()

        return messages

    except Exception as e:
        print(f"Error retrieving message thread: {str(e)}")
        return []



