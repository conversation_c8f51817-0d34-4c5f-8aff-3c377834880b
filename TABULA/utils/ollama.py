from ollama import Client

LOCAL_MODEL_DEFAULT = "gemma2:2b" # "mistral:7b-instruct"

def is_contact_italian(contact_name, contact_email):
    client = Client() # Defaults to localhost:11434

    prompt = f"Is the name '{contact_name}' with an email '{contact_email}' likely to be Italian? Answer with 'yes' or 'no' only."

    response = client.chat(model=LOCAL_MODEL_DEFAULT, messages=[
        {'role': 'user', 'content': prompt},
    ])

    llm_answer = response['message']['content'].strip().lower()

    # Only return True for clear "yes" answers
    # Consider any ambiguous response (containing both yes and no, or neither) as not Italian
    if llm_answer == "yes" or (llm_answer.startswith("yes") and "no" not in llm_answer):
        return True
    else:
        return False
