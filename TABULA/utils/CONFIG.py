#!/usr/bin/env python3
"""
Centralized configuration for Google Sheets across the TABULA project.

This module contains all Google Sheets identifiers, names, and related configuration
constants used throughout the codebase. By centralizing these values, we ensure
consistency and make maintenance easier.

Usage:
    from utils.CONFIG import LEADS_SPREADSHEET_ID, CRM_SPREADSHEET_ID, CRM_SHEET_NAME

    # Use the configuration constants in your code
    data = google_sheets.google_sheet_get_rows(CRM_SPREADSHEET_ID, CRM_SHEET_NAME)
"""

# =============================================================================
# GOOGLE SHEETS CONFIGURATION
# =============================================================================

# Spreadsheet IDs (extracted from Google Sheets URLs)
LEADS_SPREADSHEET_ID = "1JOvzsZxoXq0U_xHg4T3V5BDmzgXdDapsY3N2QxdJgcs"
CRM_SPREADSHEET_ID = "1iYBGV1x-qxOwEeHgU9EN3Bb0-PLEI2dEJsFRDPGawGw"

# Sheet Names within the spreadsheets
# Leads spreadsheet sheets
LEADS_META_SHEET_NAME = "meta"
LEADS_WEB_SHEET_NAME = "web"

# CRM spreadsheet sheets
CRM_SHEET_NAME = "CRM2025"

# =============================================================================
# WHATSAPP CONFIGURATION
# =============================================================================

# Your own WhatsApp phone number (used to identify outgoing messages)
# Format: "+393338208586" (with country code, no spaces)
# Set this to your actual WhatsApp number to improve message filtering
MY_WHATSAPP_NUMBER = "+393338208586"

# WhatsApp webhook event filtering settings
# When both 'message' and 'message.any' events are enabled, this prevents duplicates
WHATSAPP_FILTER_DUPLICATE_EVENTS = True

# =============================================================================
# CONFIGURATION VALIDATION
# =============================================================================

def validate_config():
    """
    Validate that all required configuration values are set.

    Returns:
        bool: True if all configuration is valid, False otherwise
    """
    required_configs = [
        ('LEADS_SPREADSHEET_ID', LEADS_SPREADSHEET_ID),
        ('CRM_SPREADSHEET_ID', CRM_SPREADSHEET_ID),
        ('LEADS_META_SHEET_NAME', LEADS_META_SHEET_NAME),
        ('LEADS_WEB_SHEET_NAME', LEADS_WEB_SHEET_NAME),
        ('CRM_SHEET_NAME', CRM_SHEET_NAME),
    ]

    for config_name, config_value in required_configs:
        if not config_value or not isinstance(config_value, str) or not config_value.strip():
            print(f"ERROR: {config_name} is not properly configured")
            return False

    # Validate WhatsApp configuration
    if MY_WHATSAPP_NUMBER and not MY_WHATSAPP_NUMBER.startswith('+'):
        print(f"WARNING: MY_WHATSAPP_NUMBER should start with '+' (current: {MY_WHATSAPP_NUMBER})")

    if not isinstance(WHATSAPP_FILTER_DUPLICATE_EVENTS, bool):
        print(f"ERROR: WHATSAPP_FILTER_DUPLICATE_EVENTS must be a boolean")
        return False

    return True

# =============================================================================
# CONFIGURATION INFO
# =============================================================================

def get_config_info():
    """
    Get information about the current configuration.

    Returns:
        dict: Dictionary containing configuration information
    """
    return {
        'leads_spreadsheet_id': LEADS_SPREADSHEET_ID,
        'crm_spreadsheet_id': CRM_SPREADSHEET_ID,
        'leads_meta_sheet_name': LEADS_META_SHEET_NAME,
        'leads_web_sheet_name': LEADS_WEB_SHEET_NAME,
        'crm_sheet_name': CRM_SHEET_NAME,
        'my_whatsapp_number': MY_WHATSAPP_NUMBER,
        'whatsapp_filter_duplicate_events': WHATSAPP_FILTER_DUPLICATE_EVENTS,
        'is_valid': validate_config()
    }

if __name__ == "__main__":
    # When run directly, display configuration information
    print("TABULA Configuration")
    print("=" * 40)

    config = get_config_info()
    for key, value in config.items():
        print(f"{key}: {value}")

    if config['is_valid']:
        print("\n✓ Configuration is valid")
    else:
        print("\n✗ Configuration has errors")
