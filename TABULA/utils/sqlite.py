#!/usr/bin/env python3
"""
SQLite utility module for TABULA project.

This module provides SQLite database operations for the CRM system, including:
- Database connection management
- Schema parsing from SQLite schema files
- Record synchronization and updates
- Data display utilities

Following the same structure and conventions as other utility modules
in the TABULA/utils/ directory.
"""

import os
import sys
import sqlite3
import re
from datetime import datetime
from pathlib import Path
from prettytable import PrettyTable


def get_column_types_from_schema():
    """
    Parse the CRM table column types from the SQLite schema file.
    
    This function reads the sqlite_schema.sql file and extracts column
    definitions from the CREATE TABLE "crm" statement.
    
    Returns:
        dict: Dictionary mapping column names to their SQLite data types
    """
    column_types = {}
    
    # Path to the SQLite schema file
    schema_file = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        'design', 'sqlite_schema.sql'
    )
    
    print(f"Reading column definitions from: {schema_file}")
    
    try:
        with open(schema_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the CREATE TABLE "crm" statement
        # Use regex to match the table creation with proper handling of quotes
        pattern = r'CREATE TABLE "crm"\s*\((.*?)\);'
        match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
        
        if not match:
            print("Error: Could not find CREATE TABLE \"crm\" statement in schema file")
            return {}
        
        table_definition = match.group(1)
        
        # Parse each column definition
        # Split by comma, but be careful about commas within quotes or parentheses
        lines = table_definition.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('--'):
                continue
            
            # Remove trailing comma
            line = line.rstrip(',')
            
            # Parse column definition: "column_name" TYPE
            # Handle both quoted and unquoted column names
            if line.startswith('"'):
                # Quoted column name
                end_quote = line.find('"', 1)
                if end_quote == -1:
                    continue
                column_name = line[1:end_quote]
                type_part = line[end_quote + 1:].strip()
            else:
                # Unquoted column name
                parts = line.split(None, 1)
                if len(parts) < 2:
                    continue
                column_name = parts[0]
                type_part = parts[1]
            
            # Extract the data type (first word of type definition)
            data_type = type_part.split()[0] if type_part else 'TEXT'
            
            # Clean up column name and store
            column_name = column_name.strip().replace('?', '')
            if column_name:
                column_types[column_name] = data_type.upper()
        
        print(f"Parsed {len(column_types)} column definitions from schema")
        return column_types
        
    except FileNotFoundError:
        print(f"Error: Schema file not found at {schema_file}")
        return {}
    except Exception as e:
        print(f"Error parsing schema file: {e}")
        return {}


def get_db_connection(db_path):
    """
    Create a new database connection.

    Args:
        db_path: Path to SQLite database

    Returns:
        Connection object or None if connection failed
    """
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # This enables column access by name
        return conn
    except sqlite3.Error as e:
        print(f"Error connecting to database: {e}")
        return None


def get_records_by_email(conn, table_name="crm"):
    """
    Fetch all records from the database and organize them by email.

    Args:
        conn: SQLite connection
        table_name: Name of the table to query

    Returns:
        dict: Records organized by email {email: record_dict}
    """
    records_by_email = {}

    try:
        cursor = conn.cursor()

        # Get all column names first
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [col[1] for col in cursor.fetchall()]

        # Query all records
        cursor.execute(f"SELECT * FROM {table_name}")
        rows = cursor.fetchall()

        for row in rows:
            # Convert row to dictionary
            record = {col: row[col] for col in columns}
            email = record.get("email", None)
            if email:  # Only include records with valid emails
                records_by_email[email] = record

        return records_by_email

    except sqlite3.Error as e:
        print(f"Error fetching records: {e}")
        return {}


def find_changed_columns(old_record, new_record):
    """
    Compare two records and find columns that have different values.

    Args:
        old_record: Original record from existing database
        new_record: New record from the updated source

    Returns:
        dict: Only the columns that have changed {column: new_value}
    """
    changed_columns = {}

    # Skip None/NULL values in new_record (these shouldn't overwrite existing values)
    for key, new_value in new_record.items():
        # Skip if column doesn't exist in old record (could be a new column)
        if key not in old_record:
            changed_columns[key] = new_value
            continue

        old_value = old_record[key]

        # Convert values to strings for comparison - handles different type representations
        str_new_value = str(new_value) if new_value is not None else ""
        str_old_value = str(old_value) if old_value is not None else ""

        # Compare string representations
        if str_new_value != str_old_value:
            changed_columns[key] = new_value

    return changed_columns


def update_record(conn, table_name, email, changed_columns):
    """
    Update a specific record in the database with changed columns.

    Args:
        conn: SQLite connection
        table_name: Name of table to update
        email: Email of the record to update (acting as a key)
        changed_columns: Dictionary of changed columns {column: new_value}

    Returns:
        bool: True if update was successful
    """
    if not changed_columns:
        return False  # Nothing to update

    try:
        cursor = conn.cursor()

        # Build update statement for changed columns
        set_clause = ", ".join([f'"{col}" = ?' for col in changed_columns])
        values = list(changed_columns.values())
        values.append(email)  # For the WHERE clause

        query = f'UPDATE {table_name} SET {set_clause} WHERE email = ?'
        cursor.execute(query, values)
        conn.commit()
        return True

    except sqlite3.Error as e:
        print(f"Error updating record: {e}")
        conn.rollback()
        return False


def insert_record(conn, table_name, record):
    """
    Insert a new record into the database.

    Args:
        conn: SQLite connection
        table_name: Name of table to insert into
        record: Dictionary of column values for the new record

    Returns:
        bool: True if insert was successful
    """
    try:
        cursor = conn.cursor()

        columns = list(record.keys())
        placeholders = ", ".join(["?"] * len(columns))
        columns_str = ", ".join([f'"{col}"' for col in columns])

        query = f'INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})'
        cursor.execute(query, list(record.values()))
        conn.commit()
        return True

    except sqlite3.Error as e:
        print(f"Error inserting record: {e}")
        conn.rollback()
        return False


def sync_databases(existing_db_path, temp_db_path):
    """
    Synchronize the existing database with the temporary one.
    Updates changed records and inserts new ones.

    Args:
        existing_db_path: Path to existing database file
        temp_db_path: Path to temporary database file

    Returns:
        tuple: (total_changes, new_records, updated_records)
    """
    existing_conn = get_db_connection(existing_db_path)
    temp_conn = get_db_connection(temp_db_path)

    if not existing_conn or not temp_conn:
        return 0, [], []

    try:
        # Get records from both databases
        existing_records = get_records_by_email(existing_conn)
        temp_records = get_records_by_email(temp_conn)

        # Prepare results tracking
        updated_records = []
        new_records = []

        # Process all records from temporary database
        for email, temp_record in temp_records.items():
            if email in existing_records:
                # Record exists - check for changes
                old_record = existing_records[email]
                changed_columns = find_changed_columns(old_record, temp_record)

                if changed_columns:
                    # Only update if there are actual changes
                    success = update_record(existing_conn, "crm", email, changed_columns)
                    if success:
                        # Keep track of what changed
                        change_info = {
                            "email": email,
                            "nome": temp_record.get("nome", ""),
                            "changed_columns": list(changed_columns.keys())
                        }
                        updated_records.append(change_info)
            else:
                # New record - insert it
                success = insert_record(existing_conn, "crm", temp_record)
                if success:
                    new_info = {
                        "email": email,
                        "nome": temp_record.get("nome", "")
                    }
                    new_records.append(new_info)

        total_changes = len(updated_records) + len(new_records)
        existing_conn.commit()
        return total_changes, new_records, updated_records

    except Exception as e:
        print(f"Error during database synchronization: {e}")
        return 0, [], []
    finally:
        if existing_conn:
            existing_conn.close()
        if temp_conn:
            temp_conn.close()


def display_changes_table(new_records, updated_records):
    """
    Display changes in a pretty table format.

    Args:
        new_records: List of new records added
        updated_records: List of records that were updated
    """
    if new_records:
        # Display new records
        new_table = PrettyTable()
        new_table.field_names = ["Email", "Name"]
        for record in new_records:
            new_table.add_row([record["email"], record["nome"]])

        print("\n=== NEW RECORDS ADDED ===")
        print(new_table)

    if updated_records:
        # Display updated records
        update_table = PrettyTable()
        update_table.field_names = ["Email", "Name", "Changed Columns"]
        for record in updated_records:
            columns_str = ", ".join(record["changed_columns"])
            update_table.add_row([record["email"], record["nome"], columns_str])

        print("\n=== UPDATED RECORDS ===")
        print(update_table)

    if not new_records and not updated_records:
        print("\nNo changes detected - database is already up to date.")
