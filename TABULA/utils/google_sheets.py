import os
import sys
import warnings
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import pandas as pd  # Added import for pandas
import sqlite3  # Add import for SQLite operations

# Add parent directory to path to import from utils (if needed)
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import utils.google_credentials

def get_sheets_service(credentials_file="../utils/client_secret.json", token_file="../token/token.json"):
    """
    Authenticates and returns the Google Sheets API service.
    
    Args:
        credentials_file: Path to the JSON file containing API credentials.
        token_file: Path to save/load the token.
        
    Returns:
        service: The Google Sheets API service object or None if authentication failed
    """
    
    creds = utils.google_credentials.get_google_credentials(credentials_file, token_file)
    
    if not creds:
        return None
        
    try:
        # Create and return Google Sheets API service
        service = build('sheets', 'v4', credentials=creds)
        return service
    except Exception as e:
        print(f"Error creating Google Sheets service: {e}")
        return None

def google_sheet_add_row(spreadsheet_id, sheet_name, data):
    """
    Adds a row to a Google Sheet, matching dictionary keys to column headers.
    
    Args:
        spreadsheet_id: The ID of the spreadsheet (from the URL).
        sheet_name: Name of the sheet to write to.
        data: Dictionary where keys are column headers and values are cell values.
        
    Returns:
        boolean: True if the operation was successful, False otherwise.
    """
    try:
        # Get Google Sheets service
        service = get_sheets_service()
        if not service:
            print("Failed to get Google Sheets service.")
            return False
        
        # Read the first row of the sheet to get headers
        # print(f"Getting headers from sheet: {sheet_name}")
        
        # When using get(), include the sheet name for the request
        result = service.spreadsheets().values().get(
            spreadsheetId=spreadsheet_id,
            range=f"'{sheet_name}'!A1:ZZ1" # Use only the range A1:ZZ1 without sheet name
        ).execute()
        
        # Extract header values
        headers = result.get('values', [])[0] if result.get('values') else []
        if not headers:
            print(f"No headers found in sheet '{sheet_name}'")
            return False
        
        # print(f"Found headers: {headers}")
        
        # Create a row with values in the correct order based on headers
        row_values = []
        for header in headers:
            # If the header exists in the data dictionary, use its value, otherwise empty string
            row_values.append(data.get(header, ""))
        
        # Add the row to the sheet
        value_range_body = {
            'values': [row_values]
        }
        
        # Use the range A1:ZZ for append, without the sheet name
        result = service.spreadsheets().values().append(
            spreadsheetId=spreadsheet_id,
            range=f"'{sheet_name}'!A1:ZZ",  # Use range A1:ZZ for append
            valueInputOption='USER_ENTERED',
            insertDataOption='INSERT_ROWS',
            body=value_range_body
        ).execute()
        
        print(f"Row added successfully. Updated {result.get('updates').get('updatedCells')} cells.")
        return True
        
    except HttpError as error:
        print(f"An error occurred while adding row to sheet: {error}")
        return False
    except Exception as e:
        print(f"A general error occurred: {e}")
        return False

def google_sheet_add_multiple_rows(spreadsheet_id, sheet_name, data_list):
    """
    Adds multiple rows to a Google Sheet in a single operation.
    
    Args:
        spreadsheet_id: The ID of the spreadsheet (from the URL).
        sheet_name: Name of the sheet to write to.
        data_list: List of dictionaries where keys are column headers and values are cell values.
        
    Returns:
        boolean: True if the operation was successful, False otherwise.
    """
    if not data_list:
        print("No data to add.")
        return True  # No data to add is not an error
        
    try:
        # Get Google Sheets service
        service = get_sheets_service()
        if not service:
            print("Failed to get Google Sheets service.")
            return False
        
        # Read the first row of the sheet to get headers
        # print(f"Getting headers from sheet: {sheet_name}")
        
        result = service.spreadsheets().values().get(
            spreadsheetId=spreadsheet_id,
            range=f"'{sheet_name}'!A1:ZZ1"
        ).execute()
        
        # Extract header values
        headers = result.get('values', [])[0] if result.get('values') else []
        if not headers:
            print(f"No headers found in sheet '{sheet_name}'")
            return False
        
        # print(f"Found headers: {headers}")
        
        # Create rows with values in the correct order based on headers
        rows_values = []
        for data in data_list:
            row_values = []
            for header in headers:
                # If the header exists in the data dictionary, use its value, otherwise empty string
                row_values.append(data.get(header, ""))
            rows_values.append(row_values)
        
        # Add all rows to the sheet in a single operation
        value_range_body = {
            'values': rows_values
        }
        
        result = service.spreadsheets().values().append(
            spreadsheetId=spreadsheet_id,
            range=f"'{sheet_name}'!A1:ZZ",
            valueInputOption='USER_ENTERED',
            insertDataOption='INSERT_ROWS',
            body=value_range_body
        ).execute()
        
        # print(f"Rows added successfully. Updated {result.get('updates').get('updatedCells')} cells.")
        return True
        
    except HttpError as error:
        print(f"An error occurred while adding rows to sheet: {error}")
        return False
    except Exception as e:
        print(f"A general error occurred: {e}")
        return False

def google_sheet_get_rows(spreadsheet_id, sheet_name, include_headers=True):
    """
    Gets all rows from a Google Sheet and returns them as a list of dictionaries.
    
    Args:
        spreadsheet_id: The ID of the spreadsheet (from the URL).
        sheet_name: Name of the sheet to read from.
        include_headers: Whether to include a list of headers in the result.
        
    Returns:
        tuple: (list of dictionaries representing rows, list of headers) or (None, None) if error.
    """
    try:
        # Get Google Sheets service
        service = get_sheets_service()
        if not service:
            print("Failed to get Google Sheets service.")
            return None, None
        
        # No need to quote the sheet name - the Sheets API handles this automatically
        # When using get(), we need to include the sheet name and a range
        # Use a wide range like A1:ZZ to capture all potential columns, without sheet name
        result = service.spreadsheets().values().get(
            spreadsheetId=spreadsheet_id,
            range=f"'{sheet_name}'!A1:ZZ"
        ).execute()
        
        values = result.get('values', [])
        if not values:
            print(f"No data found in sheet '{sheet_name}'")
            return [], []
        
        # First row contains headers
        headers = values[0]
        
        # Convert remaining rows to list of dictionaries
        rows = []
        for row_values in values[1:]:  # Skip the header row
            # Pad row if it's shorter than headers
            padded_row = row_values + [""] * (len(headers) - len(row_values))
            
            # Create dictionary for this row
            row_dict = {headers[i]: value for i, value in enumerate(padded_row)}
            rows.append(row_dict)
        
        if include_headers:
            return rows, headers
        return rows, None
        
    except HttpError as error:
        print(f"An error occurred while reading sheet: {error}")
        return None, None
    except Exception as e:
        print(f"A general error occurred: {e}")
        return None, None

def google_sheet_to_dataframe(spreadsheet_id, sheet_name):
    """
    Loads a Google Sheet into a pandas DataFrame for in-memory data manipulation.
    
    Args:
        spreadsheet_id: The ID of the spreadsheet (from the URL).
        sheet_name: Name of the sheet to read from.
        
    Returns:
        pandas.DataFrame: DataFrame containing the sheet data, or None if error.
    """
    try:
        # Get Google Sheets service
        service = get_sheets_service()
        if not service:
            print("Failed to get Google Sheets service.")
            return None
        
        # Get all data from the sheet
        result = service.spreadsheets().values().get(
            spreadsheetId=spreadsheet_id,
            range=f"'{sheet_name}'!A1:ZZ"
        ).execute()
        
        values = result.get('values', [])
        if not values:
            print(f"No data found in sheet '{sheet_name}'")
            return pd.DataFrame()
        
        # First row contains headers
        headers = values[0]
        
        # Convert the data to a pandas DataFrame
        if len(values) > 1:
            # Create DataFrame with all rows except the header
            df = pd.DataFrame(values[1:], columns=headers)
            
            # Try to convert numeric columns to appropriate types
            for col in df.columns:
                try:
                    # First try to convert to numeric, which handles both int and float
                    df[col] = pd.to_numeric(df[col])
                except (ValueError, TypeError):
                    # Next try to convert to datetime
                    try:
                        # Suppress the UserWarning about date format inference
                        with warnings.catch_warnings():
                            warnings.simplefilter("ignore", UserWarning)
                            df[col] = pd.to_datetime(df[col])
                    except (ValueError, TypeError):
                        # Keep as string if conversion fails
                        pass
            
            return df
        else:
            # Return empty DataFrame with headers if there's no data
            return pd.DataFrame(columns=headers)
        
    except HttpError as error:
        print(f"An error occurred while reading sheet: {error}")
        return None
    except Exception as e:
        print(f"A general error occurred: {e}")
        return None

def google_sheet_to_dataframe_with_metadata(spreadsheet_id, sheet_name):
    """
    Loads a Google Sheet into a unified DataFrame with additional cell metadata 
    (formulas and hyperlinks).
    
    Args:
        spreadsheet_id: The ID of the spreadsheet (from the URL).
        sheet_name: Name of the sheet to read from.
        
    Returns:
        pandas.DataFrame: A DataFrame where each cell's value is a dictionary containing:
            - 'value': The displayed/computed value
            - 'formula': The formula in the cell (if any)
            - 'hyperlink': The hyperlink in the cell (if any)
    """
    try:
        # Get Google Sheets service
        service = get_sheets_service()
        if not service:
            print("Failed to get Google Sheets service.")
            return None
        
        # Get sheet ID from sheet name for hyperlink retrieval
        sheet_metadata = service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
        sheets = sheet_metadata.get('sheets', [])
        sheet_id = None
        
        for sheet in sheets:
            if sheet['properties']['title'] == sheet_name:
                sheet_id = sheet['properties']['sheetId']
                break
        
        if not sheet_id:
            print(f"Sheet '{sheet_name}' not found in spreadsheet")
            return None
        
        # Get regular values
        result = service.spreadsheets().values().get(
            spreadsheetId=spreadsheet_id,
            range=f"'{sheet_name}'!A1:ZZ"
        ).execute()
        
        values = result.get('values', [])
        if not values:
            print(f"No data found in sheet '{sheet_name}'")
            return pd.DataFrame()
        
        # Get formulas
        formula_result = service.spreadsheets().values().get(
            spreadsheetId=spreadsheet_id,
            range=f"'{sheet_name}'!A1:ZZ",
            valueRenderOption='FORMULA'
        ).execute()
        
        formulas = formula_result.get('values', [])
        
        # Get hyperlinks through cell formatting - limit to the same range we used for values
        fields = 'sheets(data(rowData(values(hyperlink))))'
        formatted_data = service.spreadsheets().get(
            spreadsheetId=spreadsheet_id,
            ranges=[f"'{sheet_name}'!A1:ZZ"],
            fields=fields
        ).execute()
        
        # Extract headers (first row)
        headers = values[0] if values else []
        
        # Extract hyperlinks into a dictionary {(row, col): "url"}
        hyperlinks = {}
        try:
            sheet_data = formatted_data['sheets'][0]['data'][0]
            if 'rowData' in sheet_data:
                for row_idx, row_data in enumerate(sheet_data['rowData']):
                    if 'values' in row_data:
                        for col_idx, cell_data in enumerate(row_data['values']):
                            if 'hyperlink' in cell_data:
                                hyperlinks[(row_idx, col_idx)] = cell_data['hyperlink']
        except (KeyError, IndexError) as e:
            print(f"Error extracting hyperlinks: {e}")
        
        # Create unified DataFrame with cell objects
        # Skip the header row (row 0) in the data
        rows_data = []
        
        # Process data rows (skip headers row)
        for i in range(1, len(values)):
            row_dict = {}
            for j, header in enumerate(headers):
                # Create cell data dictionary
                cell_data = {
                    'value': values[i][j] if j < len(values[i]) else '',
                    'formula': formulas[i][j] if i < len(formulas) and j < len(formulas[i]) else '',
                    'hyperlink': hyperlinks.get((i, j), None)
                }
                
                # Try to convert values to appropriate types
                try:
                    cell_data['value'] = pd.to_numeric(cell_data['value'])
                except (ValueError, TypeError):
                    try:
                        # Suppress the UserWarning about date format inference
                        with warnings.catch_warnings():
                            warnings.simplefilter("ignore", UserWarning)
                            cell_data['value'] = pd.to_datetime(cell_data['value'])
                    except (ValueError, TypeError):
                        pass
                
                row_dict[header] = cell_data
                
            rows_data.append(row_dict)
        
        # Create DataFrame from rows
        df = pd.DataFrame(rows_data)
        
        # Add convenience methods for accessing cell data
        df.get_cell_value = lambda row_idx, col_name: df.iloc[row_idx][col_name]['value'] if row_idx < len(df) and col_name in df.columns else None
        df.get_cell_formula = lambda row_idx, col_name: df.iloc[row_idx][col_name]['formula'] if row_idx < len(df) and col_name in df.columns else None
        df.get_cell_hyperlink = lambda row_idx, col_name: df.iloc[row_idx][col_name]['hyperlink'] if row_idx < len(df) and col_name in df.columns else None
        
        # Also provide a way to extract simple value-only DataFrame
        df.get_values_dataframe = lambda: pd.DataFrame([{col: row[col]['value'] for col in df.columns} for _, row in df.iterrows()])
        
        return df
        
    except HttpError as error:
        print(f"An error occurred while reading sheet with metadata: {error}")
        return None
    except Exception as e:
        print(f"A general error occurred: {e}")
        return None

def google_sheet_to_sqlite(spreadsheet_id, sheet_name, sqlite_path, table_name='sheet_data', column_types=None):
    """
    Transforms a Google Sheet into an SQLite database file for complex querying.
    
    Args:
        spreadsheet_id: The ID of the spreadsheet (from the URL).
        sheet_name: Name of the sheet to convert.
        sqlite_path: Path where the SQLite database should be saved.
        table_name: Name of the table to create in SQLite (defaults to 'sheet_data').
        column_types: Optional dictionary mapping column names to their SQLite types.
                      Example: {'id': 'TEXT PRIMARY KEY', 'priority': 'INTEGER'}
                      
    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        print(f"Starting conversion of sheet '{sheet_name}' to SQLite...")
        
        # Use the existing function to get a DataFrame with only values
        df = google_sheet_to_dataframe(spreadsheet_id, sheet_name)
        if df is None or df.empty:
            print("No data found in the sheet or error occurred.")
            return False
            
        # Ensure the directory exists
        os.makedirs(os.path.dirname(os.path.abspath(sqlite_path)), exist_ok=True)
        
        # Create SQLite connection
        conn = sqlite3.connect(sqlite_path)
        
        try:
            cursor = conn.cursor()
            
            # If column_types is provided, create a table with those types
            if column_types:
                # Create table with specified column types
                columns_sql = []
                for col in df.columns:
                    if col in column_types:
                        columns_sql.append(f'"{col}" {column_types[col]}')
                    else:
                        columns_sql.append(f'"{col}" TEXT')
                
                create_sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns_sql)})"
                cursor.execute(create_sql)
                
                # Drop existing data
                cursor.execute(f"DELETE FROM {table_name}")
            else:
                # If no column types specified, infer types and use pandas to_sql
                # This will overwrite the table if it exists
                df.to_sql(table_name, conn, if_exists='replace', index=False)
                return True
            
            # If we have custom column types, we need to insert data manually
            if column_types:
                # Prepare data and insert rows
                placeholders = ','.join(['?'] * len(df.columns))
                insert_sql = f"INSERT INTO {table_name} VALUES ({placeholders})"
                
                # Convert DataFrame to list of tuples for batch insert
                batch_data = []
                for _, row in df.iterrows():
                    row_data = []
                    
                    for i, col in enumerate(df.columns):
                        value = row[col]
                        col_lower = col.lower()
                        
                        # Skip conversion if value is None/NaN
                        if pd.isna(value):
                            row_data.append(None)
                            continue
                            
                        # Convert values based on column types if specified
                        if col in column_types:
                            col_type = column_types[col].upper()
                            if 'INTEGER' in col_type:
                                try:
                                    row_data.append(int(value))
                                except (ValueError, TypeError):
                                    row_data.append(0)
                            elif 'REAL' in col_type or 'FLOAT' in col_type:
                                try:
                                    row_data.append(float(value))
                                except (ValueError, TypeError):
                                    row_data.append(0.0)
                            else:
                                # For TEXT and other types
                                row_data.append(str(value))
                        else:
                            # If no type specified, keep as is
                            row_data.append(value)
                            
                    batch_data.append(tuple(row_data))
                
                # Execute batch insert
                cursor.executemany(insert_sql, batch_data)
                
            conn.commit()
            row_count = cursor.execute(f"SELECT COUNT(*) FROM {table_name}").fetchone()[0]
            print(f"Successfully converted Google Sheet to SQLite database at: {sqlite_path}")
            print(f"Created table '{table_name}' with {row_count} rows and {len(df.columns)} columns.")
            return True
            
        except Exception as e:
            print(f"Error during conversion: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
            
    except Exception as e:
        print(f"A general error occurred: {e}")
        return False