import json
import os
import base64
import re
import datetime
import sys
import importlib.util
from googleapiclient.errors import HttpError
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import decode_header, make_header

# Add parent directory to path to import from utils
#sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import utils.google_credentials

# Path for storing JSON state files
STATE_DIR = "../logs"

def get_state_filename(processor_name, sender, subject, label):
    """Generate a unique state file name based on processor and search parameters."""
    # Clean up processor name, sender, subject, and label to create valid filenames
    processor_clean = re.sub(r'[^a-zA-Z0-9]', '_', processor_name) if processor_name else ''
    sender_clean = re.sub(r'[^a-zA-Z0-9]', '_', sender) if sender else ''
    subject_clean = re.sub(r'[^a-zA-Z0-9]', '_', subject) if subject else ''
    label_clean = re.sub(r'[^a-zA-Z0-9]', '_', label) if label else ''

    # Create filename based on non-empty parameters
    parts = []
    if sender_clean:
        parts.append(sender_clean)
    if subject_clean:
        parts.append(subject_clean)
    if label_clean:
        parts.append(label_clean)

    # Prepend the processor name
    return f"{processor_clean}_{'_'.join(parts)}.json"


def load_previous_state(processor_name, sender, subject, label, custom_state=None):
    """
    Load previous search state from JSON file.

    Args:
        processor_name: Name of the processor module
        sender: Email sender to search for
        subject: Subject line to search for (contains)
        label: Gmail label to search for
        custom_state: Custom state filename (without path or extension)

    Returns:
        dict: The previous state or empty dict if no state exists
    """
    # If custom_state is None (not provided), return empty state without loading
    if custom_state is None:
        return {
            "last_timestamp": None,
            "search_params": {
                "processor": processor_name,
                "sender": sender,
                "subject": subject,
                "label": label,
                "custom_state": custom_state
            }
        }

    # Ensure state directory exists
    if not os.path.exists(STATE_DIR):
        os.makedirs(STATE_DIR)

    # Use custom state filename
    state_file = os.path.join(STATE_DIR, f"{custom_state}.json")

    if os.path.exists(state_file):
        try:
            with open(state_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading previous state: {e}")

    # Return default state if no file exists or loading failed
    return {
        "last_timestamp": None,
        "search_params": {
            "processor": processor_name,
            "sender": sender,
            "subject": subject,
            "label": label,
            "custom_state": custom_state
        }
    }


def save_state(processor_name, sender, subject, label, state, custom_state=None):
    """
    Save the current search state to a JSON file.

    Args:
        processor_name: Name of the processor module
        sender: Email sender to search for
        subject: Subject line to search for (contains)
        label: Gmail label to search for
        state: State object to save
        custom_state: Custom state filename (without path or extension)
    """
    # If custom_state is None (not provided), don't save state
    if custom_state is None:
        return

    # Ensure state directory exists
    if not os.path.exists(STATE_DIR):
        os.makedirs(STATE_DIR)

    # Use custom state filename
    state_file = os.path.join(STATE_DIR, f"{custom_state}.json")

    try:
        with open(state_file, 'w') as f:
            json.dump(state, f, indent=2)
        print(f"State saved to {state_file}")
    except Exception as e:
        print(f"Error saving state: {e}")


def get_email_messages(service, sender=None, subject=None, label=None, after_timestamp=None, receiver=None):
    """
    Search Gmail for messages matching the sender, subject, and/or label.
    Also includes all messages in the same thread if a label is specified.

    Args:
        service: Gmail API service
        sender: Email address of the sender (exact match)
        subject: Subject line to search for (contains)
        label: Gmail label to search for
        after_timestamp: Only return messages after this timestamp
        receiver: Email address of the receiver (exact match)

    Returns:
        list: List of message objects
    """
    query = []

    if sender:
        query.append(f"from:{sender}")

    if subject:
        query.append(f"subject:{subject}")

    if label:
        query.append(f"label:{label}")

    if receiver:
        query.append(f"to:{receiver}")

    if after_timestamp:
        # Convert timestamp from milliseconds to seconds for Gmail API
        # Gmail API after: parameter expects Unix timestamp in seconds, not milliseconds
        timestamp_seconds = int(after_timestamp) // 1000
        query.append(f"after:{timestamp_seconds}")

    query_string = " ".join(query)
    print(f"Executing Gmail search: {query_string}")

    try:
        # Get list of messages matching the query
        results = service.users().messages().list(userId='me', q=query_string, maxResults=100).execute()
        messages = results.get('messages', [])

        # Get additional pages if available
        while 'nextPageToken' in results and len(messages) < 1000:  # Limit to 1000 messages max
            page_token = results['nextPageToken']
            results = service.users().messages().list(
                userId='me', q=query_string, pageToken=page_token, maxResults=100).execute()
            messages.extend(results.get('messages', []))

        print(f"Found {len(messages)} messages matching the search criteria.")

        # If label was specified, get all messages in the same threads
        if label and messages:
            thread_messages = []
            thread_ids = set()

            # First, collect all thread IDs from matching messages
            print("Getting thread IDs for labeled messages...")
            for msg_ref in messages:
                full_msg = get_message_content(service, msg_ref['id'])
                if full_msg and 'threadId' in full_msg:
                    thread_ids.add(full_msg['threadId'])

            print(f"Found {len(thread_ids)} conversation threads to process")

            # Now get all messages in those threads
            for thread_id in thread_ids:
                try:
                    thread = service.users().threads().get(userId='me', id=thread_id).execute()
                    if 'messages' in thread:
                        thread_messages.extend([{'id': msg['id']} for msg in thread['messages']])
                except Exception as e:
                    print(f"Error retrieving thread {thread_id}: {e}")

            print(f"Retrieved {len(thread_messages)} total messages from all threads")
            return thread_messages

        return messages

    except HttpError as error:
        print(f"An error occurred while searching messages: {error}")
        return []


def get_message_content(service, message_id):
    """
    Retrieve full content of a specific message.

    Args:
        service: Gmail API service
        message_id: ID of the message to retrieve

    Returns:
        dict: Message content or None if not found
    """
    try:
        message = service.users().messages().get(userId='me', id=message_id, format='full').execute()
        return message
    except HttpError as error:
        print(f"An error occurred while retrieving message {message_id}: {error}")
        return None


def get_message_body(message):
    """Extract the message body from a Gmail message."""
    if not message:
        return None

    payload = message.get('payload', {})

    # Function to decode message parts
    def decode_part(part):
        if part.get('body', {}).get('data'):
            return base64.urlsafe_b64decode(part['body']['data']).decode('utf-8', errors='replace')
        return ""

    # Check for multipart message
    if 'parts' in payload:
        text_parts = []
        html_parts = []

        for part in payload['parts']:
            mime_type = part.get('mimeType', '')

            if mime_type == 'text/plain':
                text_parts.append(decode_part(part))
            elif mime_type == 'text/html':
                html_parts.append(decode_part(part))
            elif 'parts' in part:
                # Handle nested parts
                for nested_part in part['parts']:
                    nested_mime_type = nested_part.get('mimeType', '')

                    if nested_mime_type == 'text/plain':
                        text_parts.append(decode_part(nested_part))
                    elif nested_mime_type == 'text/html':
                        html_parts.append(decode_part(nested_part))

        # Prefer HTML content over plain text
        if html_parts:
            return {'html': '\n'.join(html_parts), 'plain': '\n'.join(text_parts)}
        elif text_parts:
            return {'plain': '\n'.join(text_parts)}

    # Handle single part message
    elif 'body' in payload and 'data' in payload['body']:
        body_data = base64.urlsafe_b64decode(payload['body']['data']).decode('utf-8', errors='replace')

        mime_type = payload.get('mimeType', '')
        if mime_type == 'text/html':
            return {'html': body_data}
        else:
            return {'plain': body_data}

    return None


def get_message_headers(message):
    """Extract and decode email headers."""
    headers = {}

    if not message or 'payload' not in message or 'headers' not in message['payload']:
        return headers

    for header in message['payload']['headers']:
        name = header.get('name', '')
        value = header.get('value', '')

        # Decode header value if needed
        try:
            # Use make_header to handle any encoding issues
            decoded_value = str(make_header(decode_header(value)))
            headers[name.lower()] = decoded_value
        except Exception:
            headers[name.lower()] = value

    return headers

#old
def load_processor(processor_name, processors_dir):
    """
    Dynamically load a processor module.

    Args:
        processor_name: Name of the processor (filename without .py extension)

    Returns:
        module: The loaded processor module or None if not found
    """
    # Get the current directory of this script
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # Build the full path to the processor module
    processor_path = os.path.join(current_dir, processors_dir, f"{processor_name}.py")

    if not os.path.exists(processor_path):
        print(f"Error: Processor file not found: {processor_path}")
        return None

    try:
        # Load the module dynamically
        spec = importlib.util.spec_from_file_location(processor_name, processor_path)
        processor_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(processor_module)

        # Do not check yet if the module has the required process_message or process_messages functions

        return processor_module
    except Exception as e:
        print(f"Error loading processor module {processor_name}: {e}")
        return None


def gmail_processor(service, message, headers, body, processor_module):
    """
    Wrapper function that prepares data for the processor module.

    Args:
        service: Gmail API service
        message: Full message object
        headers: Extracted and decoded message headers
        body: Message body (dict with 'html' and/or 'plain' keys)
        processor_module: Module containing the process_message function

    Returns:
        dict: Processing results with success status
    """
    # print(f"Processing message with ID: {message['id']}")

    # Extract required data for the callback
    subject = headers.get('subject', '')
    from_email = headers.get('from', '')
    to_email = headers.get('to', '')

    # Get date from headers or message timestamp
    date_header = headers.get('date', '')
    if date_header:
        date = date_header
    else:
        # Fallback to internal date from Gmail
        timestamp = message.get('internalDate')
        if timestamp:
            date = datetime.datetime.fromtimestamp(int(timestamp)/1000).strftime('%a, %d %b %Y %H:%M:%S %z')
        else:
            date = datetime.datetime.now().strftime('%a, %d %b %Y %H:%M:%S %z')

    # Get HTML and plain text content
    message_body_html = body.get('html', '') if body else ''
    message_body_text = body.get('plain', '') if body else ''

    # Call the processor's process_message function with the extracted parameters
    result = processor_module.process_message(
        date=date,
        sender=from_email,
        recipient=to_email,
        subject=subject,
        message_body_html=message_body_html,
        message_body_text=message_body_text
    )

    # Add message_id to the result
    result['message_id'] = message['id']
    result['success'] = True

    return result


def process_message(service, message, processor_module):
    """
    Process a message using the specified processor module.

    Args:
        service: Gmail API service
        message: Message object with at least an 'id'
        processor_module: Module containing the process_message function

    Returns:
        dict: Results of the processing
    """
    # Get full message content
    full_message = get_message_content(service, message['id'])

    if not full_message:
        return {'success': False, 'error': 'Failed to retrieve message content'}

    # Extract message body and headers
    body = get_message_body(full_message)
    headers = get_message_headers(full_message)

    # Call the wrapper function with the message content
    try:
        result = gmail_processor(service, full_message, headers, body, processor_module)
        return result
    except Exception as e:
        print(f"Error processing message {message['id']}: {e}")
        return {'success': False, 'error': str(e)}


# old
def run_processor(sender, subject, label, processor_name, batch_mode=False, custom_state=None):
    """
    Main function to search messages and run the specified processor.

    Args:
        sender: Email sender to search for
        subject: Subject line to search for (contains)
        label: Gmail label to search for
        processor_name: Name of the processor module to use
        batch_mode: Whether to process all messages at once in batch mode
        custom_state: Custom state filename (without path or extension)
    """
    # Load the processor module
    processor_module = load_processor(processor_name)
    if not processor_module:
        return

    # Check if batch mode is requested but processor doesn't support it
    if batch_mode and not hasattr(processor_module, 'process_messages'):
        print(f"Error: Batch mode requested but processor '{processor_name}' does not implement 'process_messages' method")
        return

    # Check if individual processing is requested but processor doesn't support it
    if not batch_mode and not hasattr(processor_module, 'process_message'):
        print(f"Error: Individual processing requested but processor '{processor_name}' does not implement 'process_message' method")
        return

    # Get Gmail API service using utility module
    service = utils.google_credentials.get_gmail_service()

    if service is None:
        print("Failed to get Gmail service. Check your credentials and try again.")
        return

    # Load previous state if custom_state is provided
    state = load_previous_state(processor_name, sender, subject, label, custom_state)
    last_timestamp = state.get('last_timestamp')

    # Get new messages (list of message IDs)
    message_ids = get_email_messages(service, sender, subject, label, last_timestamp)

    if not message_ids:
        print("No new messages found")
        return

    # Fetch full message content for all IDs
    full_messages = []
    # print(f"Fetching content for {len(message_ids)} messages...")
    for msg_ref in message_ids:
        full_msg = get_message_content(service, msg_ref['id'])
        if full_msg:
            full_messages.append(full_msg)
        else:
            print(f"Skipping message {msg_ref['id']} due to fetch error.")

    # Sort messages by internalDate (oldest first)
    # Convert internalDate (string milliseconds) to integer for sorting
    full_messages.sort(key=lambda x: int(x.get('internalDate', 0)))

    print(f"Processing {len(full_messages)} messages in chronological order...")

    # Initialize variables for tracking processing
    processed_count = 0
    current_timestamp = None # Initialize timestamp for this run

    # Choose between batch mode and individual processing
    if batch_mode:
        # Prepare a list of formatted messages for batch processing
        formatted_messages = []
        for full_msg in full_messages:
            # Extract message body and headers
            body = get_message_body(full_msg)
            headers = get_message_headers(full_msg)

            # Extract timestamp from the full message
            msg_timestamp = int(full_msg.get('internalDate', 0))

            # Update the latest timestamp
            if msg_timestamp:
                current_timestamp = max(current_timestamp or 0, msg_timestamp)

            # Extract required data for the batch processing
            subject = headers.get('subject', '')
            from_email = headers.get('from', '')
            to_email = headers.get('to', '')

            # Get date from headers or message timestamp
            date_header = headers.get('date', '')
            if date_header:
                date = date_header
            else:
                timestamp = msg_timestamp
                if timestamp:
                    date = datetime.datetime.fromtimestamp(int(timestamp)/1000).strftime('%a, %d %b %Y %H:%M:%S %z')
                else:
                    date = datetime.datetime.now().strftime('%a, %d %b %Y %H:%M:%S %z')

            # Get HTML and plain text content
            message_body_html = body.get('html', '') if body else ''
            message_body_text = body.get('plain', '') if body else ''

            # Add formatted message to the list
            formatted_messages.append({
                'message_id': full_msg['id'],
                'date': date,
                'sender': from_email,
                'recipient': to_email,
                'subject': subject,
                'message_body_html': message_body_html,
                'message_body_text': message_body_text
            })

        # Process all messages at once
        try:
            result = processor_module.process_messages(formatted_messages)
            if result.get('success'):
                processed_count = result.get('processed_count', len(formatted_messages))
                # print(f"Successfully processed {processed_count} messages in batch mode")
            else:
                print(f"Batch processing failed: {result.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"Error in batch processing: {e}")
    else:
        # Process each message individually in sorted order
        for full_msg in full_messages:
            # Extract timestamp from the full message
            msg_timestamp = None
            if 'internalDate' in full_msg:
                msg_timestamp = int(full_msg['internalDate'])

            # Process the message
            result = process_message(service, full_msg, processor_module)

            if result.get('success'):
                processed_count += 1

                # Update timestamp to the latest processed message's timestamp
                if msg_timestamp:
                    current_timestamp = max(current_timestamp or 0, msg_timestamp)
                    # print(f"Message {full_msg['id']} timestamp: {msg_timestamp} ({datetime.datetime.fromtimestamp(msg_timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')})")
            else:
                 print(f"Failed to process message {full_msg['id']}: {result.get('error', 'Unknown error')}")

    # Update and save state if we processed any messages and custom_state is provided
    if processed_count > 0:
        # Use the timestamp of the *last* processed message (most recent)
        if current_timestamp:
            # Only update state if the new timestamp is greater than the last saved one
            if last_timestamp is None or current_timestamp > last_timestamp:
                state['last_timestamp'] = current_timestamp
                # Remove the processed_ids field if it exists from previous version
                if 'processed_ids' in state:
                    del state['processed_ids']
                save_state(processor_name, sender, subject, label, state, custom_state)
                # print(f"Processed {processed_count} new messages.")
                # print(f"Updated last timestamp to: {current_timestamp} ({datetime.datetime.fromtimestamp(current_timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')})")
            else:
                 print(f"Processed {processed_count} messages, but no newer timestamp found ({current_timestamp} <= {last_timestamp}). State not updated.")
        else:
            print(f"Processed {processed_count} new messages, but couldn't determine timestamp. State not updated.")
    else:
        print("No new messages were processed.")


# This file is now a utility library for Gmail processing
# It should be imported by other scripts, not run directly