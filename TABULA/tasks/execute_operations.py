#!/usr/bin/env python3
# Script to send emails to customers from CRM sheet based on the "op" column
import os
import sys
import argparse
from prettytable import PrettyTable

# Add parent directory to path to import utils
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from utils import google_sheets
from utils import ollama
from utils import waha
from utils import gmail_send
from utils.CONFIG import CRM_SPREADSHEET_ID, CRM_SHEET_NAME

def get_language(full_name, email):
    """
    Determine the language based on customer information.
    Currently uses is_italian() but can be extended for more languages.

    Args:
        full_name (str): The customer's full name
        email (str): The customer's email address

    Returns:
        str: Two-letter country code in uppercase (e.g., "IT", "EN")
    """
    is_italian = ollama.is_contact_italian(full_name, email)
    if is_italian:
        return "IT"
    else:
        return "EN"

def extract_name(full_name):
    """
    Extract the first name from the full name for personalization.

    Args:
        full_name (str): The full name from the CRM.

    Returns:
        str: The extracted name for personalization.
    """
    if not full_name:
        return ""

    # Split by spaces and get the first part
    parts = full_name.strip().split()
    if not parts:
        return ""

    # Use first part (likely first name)
    return parts[0]

def load_template(template_path):
    """
    Load a template from a file.

    Args:
        template_path (str): The full path to the template file.

    Returns:
        str: The template content or None if file not found.
    """
    try:
        with open(template_path, "r") as f:
            return f.read()
    except FileNotFoundError:
        print(f"Template file not found: {template_path}")
        return None

def send_emails(draft=False):
    """
    Process data from CRM sheet and send emails based on the "op" column

    Args:
        draft (bool): If True, create drafts instead of sending emails

    Returns:
        dict: Results with success status and counts of processed and sent emails
    """
    print(f"Processing CRM data for {'creating draft emails' if draft else 'sending emails'}")

    try:
        # Get the sheet service
        service = google_sheets.get_sheets_service()
        if not service:
            print("Failed to get Google Sheets service.")
            return {'success': False, 'error': 'Sheet service unavailable'}

        # Read all rows from the CRM sheet
        crm_data, crm_headers = google_sheets.google_sheet_get_rows(CRM_SPREADSHEET_ID, CRM_SHEET_NAME)
        if crm_data is None:
            print(f"Failed to read CRM sheet {CRM_SHEET_NAME}")
            return {'success': False, 'error': 'Failed to read CRM sheet'}

        print(f"Retrieved {len(crm_data)} rows from CRM sheet")

        # Verify required columns exist
        required_columns = ["op", "email", "nome", "op_history", "phone_number"]
        for column in required_columns:
            if column not in crm_headers:
                print(f"Error: '{column}' column not found in CRM sheet")
                return {'success': False, 'error': f"'{column}' column missing"}

        op_history_column = "op_history"
        phone_column = "phone_number"

        # Create a list to store results of email operations
        processed_count = 0
        sent_count = 0
        email_results = []
        whatsapp_results = []

        # Define the templates directory - now using ../templates/ relative to script
        templates_dir = os.path.join(parent_dir, "templates")

        # Process each row in CRM data
        for row in crm_data:
            # Skip if no "op" value or it's empty
            if "op" not in row or not row["op"]:
                continue

            processed_count += 1

            # Get customer data
            email = row.get("email", "").strip()
            full_name = row.get("nome", "").strip()
            op_value = row.get("op", "").strip()

            if not full_name.isascii():
                print(f"Skipping non-ASCII name: {full_name}")
                full_name=""

            # Get phone number if available
            phone_number = None
            if phone_column and phone_column in row:
                phone_number = row.get(phone_column, "").strip()
                # Handle phone number formatting
                if phone_number:
                    if phone_number.startswith("p:+"):
                        phone_number = phone_number[3:]  # Remove the "p:+" prefix
                    elif phone_number.startswith("p:"):
                        phone_number = phone_number[2:]  # Remove the "p:" prefix
                    elif not phone_number.startswith("+"):
                        phone_number = "+" + phone_number  # Add + if not present

            # Skip if no email
            if not email:
                print(f"Skipping row with missing email")
                continue

            # Extract name for personalization
            name = extract_name(full_name)

            # Determine language using the new get_language method
            mail_language = get_language(full_name, email)
            print(f"Contact language detected: {mail_language}")

            # Generate template names based on op value and language
            email_template_name = f"{op_value}_mail_{mail_language}.txt"
            wa_template_name = f"{op_value}_wa_{mail_language}.txt"

            # Build full paths to templates
            email_template_path = os.path.join(templates_dir, email_template_name)
            wa_template_path = os.path.join(templates_dir, wa_template_name)

            # Check if email template exists
            email_template_exists = os.path.exists(email_template_path)
            wa_template_exists = os.path.exists(wa_template_path) if phone_number else False

            # If neither template exists, skip this record
            if not email_template_exists and not wa_template_exists:
                print(f"Skipping {email} due to missing templates: email={email_template_name}, wa={wa_template_name}")
                email_results.append({
                    "email": email,
                    "name": name,
                    "template": email_template_name,
                    "status": "skipped - all templates missing"
                })
                continue

            # Process has_updates flag to track if we need to update the sheet
            has_updates = False

            # Send email if template exists
            if email_template_exists:
                print(f"Sending {email_template_name} to {email} ({full_name})")

                # Prepare email send parameters
                sender_email = "S-ARGAME Immersive Games <<EMAIL>>"

                # Load the email template
                template_content = load_template(email_template_path)
                if not template_content:
                    print(f"Failed to load email template: {email_template_path}")
                    email_results.append({
                        "email": email,
                        "name": name,
                        "template": email_template_name,
                        "status": "failed - template load error"
                    })
                    continue

                # Replace placeholders in the template
                message_body = template_content.replace("{{name}}", name)

                # Extract Subject from template
                subject_line = ""
                for line in template_content.splitlines():
                    if line.startswith("Subject:"):
                        subject_line = line[8:].strip()  # remove "Subject:" and leading/trailing spaces
                        break
                if not subject_line:
                    subject_line = f"Email for {name}"  # default subject if not found in template

                # Send the email using the gmail_send library
                try:
                    message_id = gmail_send.create_mail(
                        sender_email=sender_email,
                        recipient_email=email,
                        subject=subject_line,
                        message_body=message_body,
                        is_html=True,
                        label="02_S-ARGAME/LEADS",
                        draft=draft  # Create draft if draft mode, send directly otherwise
                    )

                    if message_id:
                        # Log success
                        action = "Draft created" if draft else "Email sent"
                        print(f"{action} successfully for {email}")
                        email_results.append({
                            "email": email,
                            "name": name,
                            "template": email_template_name,
                            "status": "success"
                        })
                        sent_count += 1
                        has_updates = True
                    else:
                        print(f"Failed to {'create draft' if draft else 'send email'} for {email}")
                        email_results.append({
                            "email": email,
                            "name": name,
                            "template": email_template_name,
                            "status": "failed"
                        })

                except Exception as e:
                    print(f"Error {'creating draft' if draft else 'sending email'} for {email}: {e}")
                    email_results.append({
                        "email": email,
                        "name": name,
                        "template": email_template_name,
                        "status": "failed",
                        "error": str(e)
                    })
            else:
                print(f"Skipping email to {email} - template missing: {email_template_name}")
                email_results.append({
                    "email": email,
                    "name": name,
                    "template": email_template_name,
                    "status": "skipped - template missing"
                })

            # Send WhatsApp message if phone number is available and template exists
            if phone_number and wa_template_exists:
                # Load WhatsApp template
                wa_template_content = load_template(wa_template_path)

                # Replace placeholders if any
                message = wa_template_content.replace("{{name}}", name)

                try:
                    # Send the WhatsApp message using waha.py
                    wa_result = waha.send_whatsapp_message(phone_number, message)

                    whatsapp_status = "success"
                    whatsapp_results.append({
                        "phone": phone_number,
                        "name": name,
                        "template": wa_template_name,
                        "status": whatsapp_status
                    })

                    print(f"WhatsApp message {whatsapp_status} to {phone_number}")
                    has_updates = True
                except Exception as e:
                    print(f"Error sending WhatsApp message to {phone_number}: {str(e)}")
                    whatsapp_results.append({
                        "phone": phone_number,
                        "name": name,
                        "template": wa_template_name,
                        "status": "exception",
                        "error": str(e)
                    })
            elif phone_number:
                print(f"Skipped WhatsApp to {phone_number} - template missing: {wa_template_name}")
                whatsapp_results.append({
                    "phone": phone_number,
                    "name": name,
                    "template": wa_template_name,
                    "status": "skipped - template missing"
                })

            # Update sheet only if something was sent successfully
            if has_updates:
                try:
                    # Prepare data for sheet update (will be done in batch later)
                    # Find the row index in the sheet
                    row_index = crm_data.index(row) + 2  # +2 for header row and 1-indexed

                    # Clear the "op" column to avoid re-sending
                    op_col_index = crm_headers.index("op") + 1  # 1-indexed column
                    op_cell_range = f"{CRM_SHEET_NAME}!{chr(64 + op_col_index)}{row_index}"

                    # Add to batch update requests
                    if 'sheet_updates' not in locals():
                        sheet_updates = []

                    # Add op column update to batch
                    sheet_updates.append({
                        'range': op_cell_range,
                        'values': [[""]]
                    })

                    # Update op_history column to append the operation
                    op_history_column = "op_history"
                    if op_history_column in crm_headers:
                        # Get current op_history value
                        current_op_history = row.get(op_history_column, "").strip()

                        # Check if current operation is already in history
                        op_history_entries = [entry.strip() for entry in current_op_history.split(",")] if current_op_history else []

                        # Only append if the operation is not already in history
                        if op_value not in op_history_entries:
                            # Add operation to history
                            if current_op_history:
                                new_op_history = f"{current_op_history},{op_value}"
                            else:
                                new_op_history = op_value

                            # Find the op_history column index
                            op_history_col_index = crm_headers.index(op_history_column) + 1  # 1-indexed column

                            # Define the range for the op_history column
                            history_cell_range = f"{CRM_SHEET_NAME}!{chr(64 + op_history_col_index)}{row_index}"

                            # Add op_history update to batch
                            sheet_updates.append({
                                'range': history_cell_range,
                                'values': [[new_op_history]]
                            })

                            print(f"Prepared operation history update for {email}: '{current_op_history}' -> '{new_op_history}'")
                        else:
                            print(f"Operation '{op_value}' already in history for {email}, not updating")

                except Exception as e:
                    print(f"Error updating sheet after processing {email}: {str(e)}")

        # Perform batch update of the Google Sheet if we have updates
        if 'sheet_updates' in locals() and sheet_updates:
            print(f"\nApplying {len(sheet_updates)} updates to the Google Sheet in a single batch request...")
            try:
                # Create the data for batch update
                batch_update_data = {
                    'valueInputOption': 'USER_ENTERED',
                    'data': sheet_updates
                }

                # Execute the batch update
                batch_result = service.spreadsheets().values().batchUpdate(
                    spreadsheetId=CRM_SPREADSHEET_ID,
                    body=batch_update_data
                ).execute()

                print(f"Successfully updated {batch_result.get('totalUpdatedCells', 0)} cells in the CRM sheet")
            except Exception as e:
                print(f"Error performing batch update to CRM sheet: {str(e)}")

        # Create a pretty table to display email results
        if email_results:
            table = PrettyTable()
            table.field_names = ["Email", "Name", "Template", "Status"]
            for result in email_results:
                table.add_row([
                    result["email"],
                    result["name"],
                    result["template"],
                    result["status"]
                ])
            print("\nEmail Sending Results:")
            print(table)

        # Create a pretty table to display WhatsApp results
        if whatsapp_results:
            table = PrettyTable()
            table.field_names = ["Phone", "Name", "Template", "Status"]
            for result in whatsapp_results:
                table.add_row([
                    result["phone"],
                    result["name"],
                    result["template"],
                    result["status"]
                ])
            print("\nWhatsApp Sending Results:")
            print(table)

        return {
            'success': True,
            'processed_count': processed_count,
            'sent_count': sent_count,
            'email_results': email_results,
            'whatsapp_results': whatsapp_results
        }

    except Exception as e:
        print(f"Error processing CRM data: {e}")
        return {'success': False, 'error': str(e)}

def main():
    """Main function to run the script"""
    print("Starting execute_operations.py")

    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Send emails to customers from CRM sheet")
    parser.add_argument("--draft", action="store_true", help="Create drafts instead of sending emails")
    args = parser.parse_args()

    result = send_emails(draft=args.draft)

    if result['success']:
        print(f"{'Created' if args.draft else 'Sent'} {result['sent_count']} emails")
        if 'whatsapp_results' in result:
            whatsapp_sent = sum(1 for r in result.get('whatsapp_results', []) if r.get('status') == 'success')
            print(f"Sent {whatsapp_sent} WhatsApp messages")
    else:
        print(f"Failed to process CRM data: {result.get('error', 'Unknown error')}")

    print("Completed execute_operations.py")

if __name__ == "__main__":
    main()