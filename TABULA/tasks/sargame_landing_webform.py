import re
import datetime
import os
import sys
import argparse
from bs4 import BeautifulSoup
from prettytable import PrettyTable

# Add parent directory to path to import utils and gmail_process
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils import google_credentials
from utils import google_sheets
from utils import gmail_process
from utils.CONFIG import LEADS_SPREADSHEET_ID, LEADS_WEB_SHEET_NAME

def extract_form_data(message_body_html, message_body_text, date, subject):
    """
    Extract form data from email content.

    Args:
        message_body_html: HTML body content
        message_body_text: Plain text body content
        date: Email date
        subject: Email subject

    Returns:
        dict: Form data extracted from email
    """
    # Create a dictionary to store form data
    form_data = {}

    # Extract form fields from HTML
    if message_body_html:

        # Parse HTML using BeautifulSoup
        soup = BeautifulSoup(message_body_html, 'html.parser')

        # Find all table rows - WPForms uses table structure for form data
        rows = soup.find_all('tr')

        # Process each row to extract field name and value
        for i in range(0, len(rows) - 1, 2):  # Fields are in pairs of rows
            if i + 1 < len(rows):
                # Get the field name from the first row
                field_name_td = rows[i].find('td', class_='field-name')
                if field_name_td and field_name_td.find('strong'):
                    field_name = field_name_td.find('strong').get_text().strip()

                    # Get the field value from the next row
                    field_value_td = rows[i + 1].find('td', class_='field-value')
                    if field_value_td:
                        field_value = field_value_td.get_text().strip()

                        # Clean up field name and add to dictionary (lowercase with underscores)
                        if "Are you a Business owner?" in field_name:
                            form_data["business_owner"] = field_value
                        elif "Name and Surname" in field_name:
                            form_data["name"] = field_value
                        elif "Telephone / Whatsapp" in field_name:
                            form_data["phone"] = field_value
                        elif "Company" in field_name:
                            form_data["company"] = field_value
                        elif "Email" in field_name:
                            form_data["email"] = field_value
                        elif "Request reason" in field_name:
                            form_data["request_reason"] = field_value
                        elif "Request" in field_name:
                            form_data["request"] = field_value
                        elif "GDPR Acceptance" in field_name:
                            form_data["gdpr_accepted"] = "Yes"

    # Fallback to text if HTML parsing didn't work or returned incomplete data
    if not form_data and message_body_text:
        print("Falling back to plain text content")

        # Extract form fields using regex patterns (lowercase with underscores)
        name_match = re.search(r'Name and Surname[^\n]*\n.*?([^\n]+)', message_body_text)
        if name_match:
            form_data["name"] = name_match.group(1).strip()

        phone_match = re.search(r'Telephone / Whatsapp[^\n]*\n.*?([^\n]+)', message_body_text)
        if phone_match:
            form_data["phone"] = phone_match.group(1).strip()

        company_match = re.search(r'Company[^\n]*\n.*?([^\n]+)', message_body_text)
        if company_match:
            form_data["company"] = company_match.group(1).strip()

        email_match = re.search(r'Email[^\n]*\n.*?([^\n]+)', message_body_text)
        if email_match:
            form_data["email"] = email_match.group(1).strip()

        business_owner_match = re.search(r'Are you a Business owner\?[^\n]*\n.*?([^\n]+)', message_body_text)
        if business_owner_match:
            form_data["business_owner"] = business_owner_match.group(1).strip()

        request_reason_match = re.search(r'Request reason[^\n]*\n.*?([^\n]+)', message_body_text)
        if request_reason_match:
            form_data["request_reason"] = request_reason_match.group(1).strip()

        request_match = re.search(r'Request[^\n]*\n.*?([^\n]+)', message_body_text)
        if request_match:
            form_data["request"] = request_match.group(1).strip()

        if "GDPR Acceptance" in message_body_text:
            form_data["gdpr_accepted"] = "Yes"

    # Add additional metadata to the form data (lowercase with underscores)
    form_data["submission_date"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    form_data["source"] = "Website Form"
    form_data["email_date"] = date
    form_data["email_subject"] = subject

    # Prepend "p:" to phone number if it exists
    if "phone" in form_data and form_data["phone"]:
        form_data["phone"] = f"p:{form_data['phone']}"

    return form_data

def process_messages(messages_array):
    """
    Process multiple webform emails at once in batch mode.

    Args:
        messages_array: List of message dictionaries with the following keys:
            - message_id: Unique message ID
            - date: Email date string
            - sender: Email sender
            - recipient: Email recipient
            - subject: Email subject
            - message_body_html: HTML body content
            - message_body_text: Plain text body content

    Returns:
        dict: Processing results with success status and number of processed messages
    """
    if not messages_array:
        return {'success': True, 'processed_count': 0, 'successful_count': 0, 'results': []}

    # Extract form data from all messages
    processed_forms = []

    for message in messages_array:
        try:
            form_data = extract_form_data(
                message['message_body_html'],
                message['message_body_text'],
                message['date'],
                message['subject']
            )

            processed_forms.append({
                'message_id': message['message_id'],
                'form_data': form_data
            })

        except Exception as e:
            processed_forms.append({
                'message_id': message['message_id'],
                'error': str(e),
                'success': False
            })

    # Fetch existing leads data once
    existing_rows, _ = google_sheets.google_sheet_get_rows(LEADS_SPREADSHEET_ID, LEADS_WEB_SHEET_NAME)

    # Create set of existing emails for faster lookups
    existing_emails = {row.get("email") for row in existing_rows if row.get("email")}

    # Process forms with deduplication by email
    unique_forms = {}  # Dictionary to store unique forms by email
    results = []
    processed_count = len(processed_forms)
    successful_count = 0

    for processed_form in processed_forms:
        message_id = processed_form.get('message_id')

        if 'error' in processed_form:
            results.append(processed_form)
            continue

        form_data = processed_form['form_data']
        email = form_data.get("email")
        phone = form_data.get("phone")

        # Create result structure
        result = {
            'message_id': message_id,
            'timestamp': datetime.datetime.now().isoformat(),
            'form_data': form_data,
            'added_to_sheet': False,
            'already_exists': False,
            'success': False
        }

        # Skip if missing required data
        if not "name" in form_data or not (email or phone):
            results.append(result)
            continue

        # Check if already exists in the sheet
        already_exists = False
        if email:
            # Simple check if email is in our set of existing emails
            if email in existing_emails:
                already_exists = True
            # Fallback check for phone matches (in case email is new but phone exists)
            elif phone:
                for row in existing_rows:
                    if row.get("phone") == phone:
                        already_exists = True
                        break
        elif phone:  # No email but has phone
            for row in existing_rows:
                if row.get("phone") == phone:
                    already_exists = True
                    break

        if already_exists:
            result['already_exists'] = True
            result['success'] = True
            successful_count += 1
            results.append(result)
            continue

        # If not already in sheet, add to unique forms dictionary keyed by email or phone
        # This ensures we only keep one form per unique email/phone in this batch
        if email:
            unique_forms[email] = form_data
            result['added_to_sheet'] = True
            result['success'] = True
            successful_count += 1
        elif phone:  # No email but has phone
            phone_key = f"phone:{phone}"
            unique_forms[phone_key] = form_data
            result['added_to_sheet'] = True
            result['success'] = True
            successful_count += 1

        results.append(result)

    # Prepare list of unique forms to add
    forms_to_add = list(unique_forms.values())

    # Batch add all new unique forms in one operation
    if forms_to_add:
        added_success = google_sheets.google_sheet_add_multiple_rows(
            LEADS_SPREADSHEET_ID,
            LEADS_WEB_SHEET_NAME,
            forms_to_add
        )

        # Update results if batch add failed
        if not added_success:
            for result in results:
                if result.get('added_to_sheet'):
                    result['added_to_sheet'] = False
                    result['success'] = False
                    successful_count -= 1

    # Count the number of newly inserted rows
    newly_inserted_count = sum(1 for result in results if result.get('success') and result.get('added_to_sheet'))

    print(f"\nProcessed Form Submissions: {newly_inserted_count} new rows inserted")

    return {
        'success': True,
        'processed_count': processed_count,
        'successful_count': successful_count,
        'results': results
    }


def main():
    """Command-line entry point for webform email processing."""
    parser = argparse.ArgumentParser(description="Process Gmail messages for webform submissions")
    parser.add_argument('--sender', help='Email address of the sender to search for')
    parser.add_argument('--subject', help='Subject line to search for (contains)')
    parser.add_argument('--label', help='Gmail label to search for')
    parser.add_argument('--reset', action='store_true',
                      help='Reset search state (process all matching emails)')
    parser.add_argument('--batch', action='store_true', default=True,
                      help='Process messages in batch mode (default: True)')
    parser.add_argument('--state', help='Custom state file name to use instead of auto-generated one')

    args = parser.parse_args()

    # Check that at least one filter is provided
    if not args.sender and not args.subject and not args.label:
        parser.error("At least one filter (--sender, --subject, or --label) must be provided")

    # Reset state if requested
    if args.reset:
        if args.state:
            # Use custom state filename if provided
            state_file = os.path.join(gmail_process.STATE_DIR, f"{args.state}.json")
            if os.path.exists(state_file):
                os.remove(state_file)
                print(f"Reset state file: {state_file}")
        else:
            # Use auto-generated filename
            state_file = os.path.join(gmail_process.STATE_DIR,
                                    gmail_process.get_state_filename("sargame_landing_webform", args.sender, args.subject, args.label))
            if os.path.exists(state_file):
                os.remove(state_file)
                print(f"Reset state for webform processor and filters: sender='{args.sender or ''}', subject='{args.subject or ''}', label='{args.label or ''}'")

    # Get Gmail API service using utility module
    service = google_credentials.get_gmail_service()

    if service is None:
        print("Failed to get Gmail service. Check your credentials and try again.")
        return

    # Load previous state if custom_state is provided
    state = gmail_process.load_previous_state("sargame_landing_webform", args.sender, args.subject, args.label, args.state)
    last_timestamp = state.get('last_timestamp')

    # Get new messages (list of message IDs)
    message_ids = gmail_process.get_email_messages(service, args.sender, args.subject, args.label, last_timestamp)

    if not message_ids:
        print("No new messages found")
        return

    # Fetch full message content for all IDs
    full_messages = []
    for msg_ref in message_ids:
        full_msg = gmail_process.get_message_content(service, msg_ref['id'])
        if full_msg:
            full_messages.append(full_msg)
        else:
            print(f"Skipping message {msg_ref['id']} due to fetch error.")

    # Sort messages by internalDate (oldest first)
    full_messages.sort(key=lambda x: int(x.get('internalDate', 0)))

    print(f"Processing {len(full_messages)} messages in chronological order...")

    # Prepare a list of formatted messages for batch processing
    formatted_messages = []
    current_timestamp = None

    for full_msg in full_messages:
        # Extract message body and headers
        body = gmail_process.get_message_body(full_msg)
        headers = gmail_process.get_message_headers(full_msg)

        # Extract timestamp from the full message
        msg_timestamp = int(full_msg.get('internalDate', 0))

        # Update the latest timestamp
        if msg_timestamp:
            current_timestamp = max(current_timestamp or 0, msg_timestamp)

        # Extract required data for the batch processing
        subject = headers.get('subject', '')
        from_email = headers.get('from', '')
        to_email = headers.get('to', '')

        # Get date from headers or message timestamp
        date_header = headers.get('date', '')
        if date_header:
            date = date_header
        else:
            timestamp = msg_timestamp
            if timestamp:
                date = datetime.datetime.fromtimestamp(int(timestamp)/1000).strftime('%a, %d %b %Y %H:%M:%S %z')
            else:
                date = datetime.datetime.now().strftime('%a, %d %b %Y %H:%M:%S %z')

        # Get HTML and plain text content
        message_body_html = body.get('html', '') if body else ''
        message_body_text = body.get('plain', '') if body else ''

        # Add formatted message to the list
        formatted_messages.append({
            'message_id': full_msg['id'],
            'date': date,
            'sender': from_email,
            'recipient': to_email,
            'subject': subject,
            'message_body_html': message_body_html,
            'message_body_text': message_body_text
        })

    # Process all messages at once
    processed_count = 0
    try:
        result = process_messages(formatted_messages)
        if result.get('success'):
            processed_count = result.get('processed_count', len(formatted_messages))
        else:
            print(f"Batch processing failed: {result.get('error', 'Unknown error')}")
    except Exception as e:
        print(f"Error in batch processing: {e}")

    # Update and save state if we processed any messages and custom_state is provided
    if processed_count > 0:
        # Use the timestamp of the *last* processed message (most recent)
        if current_timestamp:
            # Only update state if the new timestamp is greater than the last saved one
            if last_timestamp is None or current_timestamp > last_timestamp:
                state['last_timestamp'] = current_timestamp
                # Remove the processed_ids field if it exists from previous version
                if 'processed_ids' in state:
                    del state['processed_ids']
                gmail_process.save_state("sargame_landing_webform", args.sender, args.subject, args.label, state, args.state)
            else:
                print(f"Processed {processed_count} messages, but no newer timestamp found ({current_timestamp} <= {last_timestamp}). State not updated.")
        else:
            print(f"Processed {processed_count} new messages, but couldn't determine timestamp. State not updated.")
    else:
        print("No new messages were processed.")


if __name__ == "__main__":
    main()