#!/usr/bin/env python3
# Script to pull contacts from Meta sheet to CRM sheet
import os
import sys
import datetime
from prettytable import PrettyTable

# Add parent directory to path to import utils
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from utils import google_credentials
from utils import google_sheets
from utils.CONFIG import (
    LEADS_SPREADSHEET_ID,
    LEADS_META_SHEET_NAME,
    LEADS_WEB_SHEET_NAME,
    CRM_SPREADSHEET_ID,
    CRM_SHEET_NAME
)

def choose_operation(is_business):
    """
    Determine the operation code based on user type

    Args:
        is_business (bool): Whether the user is a business user

    Returns:
        str: Operation code - "1b" for business users, "1h" for home users
    """
    return "1b" if is_business else "1h"

def pull_contacts_from_meta():
    """
    Pull contacts from Meta sheet and add them to CRM sheet if they don't already exist

    Returns:
        dict: Results with success status and counts of processed and added contacts
    """
    print(f"Pulling contacts from Meta sheet to CRM")

    try:
        # Get the sheet service
        service = google_sheets.get_sheets_service()
        if not service:
            print("Failed to get Google Sheets service.")
            return {'success': False, 'error': 'Sheet service unavailable'}

        # Read all rows from the Meta sheet
        meta_data, meta_headers = google_sheets.google_sheet_get_rows(LEADS_SPREADSHEET_ID, LEADS_META_SHEET_NAME)
        if meta_data is None:
            print(f"Failed to read Meta sheet {LEADS_META_SHEET_NAME}")
            return {'success': False, 'error': 'Failed to read Meta sheet'}

        print(f"Retrieved {len(meta_data)} rows from Meta sheet")

        # Read all rows from the CRM sheet
        crm_data, crm_headers = google_sheets.google_sheet_get_rows(CRM_SPREADSHEET_ID, CRM_SHEET_NAME)
        if crm_data is None:
            print(f"Failed to read CRM sheet {CRM_SHEET_NAME}")
            return {'success': False, 'error': 'Failed to read CRM sheet'}

        print(f"Retrieved {len(crm_data)} rows from CRM sheet")

        # Verify email column exists in both sheets
        if 'email' not in meta_headers or 'email' not in crm_headers:
            print("Error: 'email' column not found in one or both sheets")
            return {'success': False, 'error': 'Email column missing'}

        # Phone column is always "phone_number" in CRM sheet
        phone_column = "phone_number"

        print(f"Using phone column in CRM sheet: {phone_column}")

        # Create a set of existing emails in CRM for fast lookup
        existing_emails = {row['email'].lower().strip() for row in crm_data if 'email' in row and row['email'].strip()}

        # Create a set of existing phone numbers in CRM for fast lookup
        existing_phones = set()
        if phone_column:
            existing_phones = {
                row[phone_column].strip()
                for row in crm_data
                if phone_column in row and row[phone_column] and row[phone_column].strip()
            }
            # Normalize phone numbers for comparison (remove spaces, etc.)
            existing_phones = {phone.replace(' ', '').replace('-', '') for phone in existing_phones}

        # Find common columns between the two sheets
        common_columns = set(meta_headers).intersection(set(crm_headers))
        print(f"Found {len(common_columns)} common columns between sheets")

        # Prepare rows to add
        rows_to_add = []
        contacts_to_add = []

        # Get the column indices in CRM sheet
        crm_column_indices = {header: i for i, header in enumerate(crm_headers)}

        # Process each contact in Meta sheet
        for contact in meta_data:
            # Skip if no email or email is empty
            if 'email' not in contact or not contact['email'].strip():
                continue

            email = contact['email'].lower().strip()

            # Get phone number if available
            phone = None
            meta_phone_column = None

            # Look for phone column in Meta sheet
            for potential_phone_column in ['phone_number', 'mobile', 'tel', 'phone']:
                if potential_phone_column in contact and contact[potential_phone_column]:
                    meta_phone_column = potential_phone_column
                    phone = contact[potential_phone_column].strip()
                    # Normalize phone (remove spaces, etc.)
                    normalized_phone = phone.replace(' ', '').replace('-', '')
                    break

            # Skip if email already exists in CRM
            if email in existing_emails:
                continue

            # Skip if phone already exists in CRM (if we found a phone for this contact)
            if phone and phone_column and normalized_phone in existing_phones:
                print(f"Skipping contact with email {email} - phone {phone} already exists in CRM")
                continue

            # Create a new row with empty cells
            new_row = [""] * len(crm_headers)

            # Fill in common columns
            for column in common_columns:
                if column in contact and contact[column] is not None:
                    column_index = crm_column_indices[column]
                    new_row[column_index] = contact[column]

            # Determine if user is business or home, only looking at "business_or_home_user?" column
            is_business = False
            if 'business_or_home_user?' in contact and contact['business_or_home_user?']:
                business_value = str(contact['business_or_home_user?']).lower()
                is_business = business_value == 'business'

            # Set the "op" column with the appropriate code if it exists
            if "op" in crm_column_indices:
                new_row[crm_column_indices["op"]] = choose_operation(is_business)

            rows_to_add.append(new_row)
            contacts_to_add.append(email)

        # Add new rows to CRM sheet if any
        if rows_to_add:
            # Find the first empty row
            first_empty_row = len(crm_data) + 2  # +2 for header row and 1-indexed

            # Calculate end column letter properly for sheets with many columns
            def column_letter(col_index):
                """Convert a column number to a column letter (A, B, C, ..., Z, AA, AB, ...)"""
                letter = ""
                while col_index > 0:
                    col_index, remainder = divmod(col_index - 1, 26)
                    letter = chr(65 + remainder) + letter
                return letter

            # Prepare the range with proper column letters
            last_column_letter = column_letter(len(crm_headers))
            range_name = f"{CRM_SHEET_NAME}!A{first_empty_row}:{last_column_letter}{first_empty_row + len(rows_to_add) - 1}"

            # Update the sheet
            request = service.spreadsheets().values().update(
                spreadsheetId=CRM_SPREADSHEET_ID,
                range=range_name,
                valueInputOption="USER_ENTERED",
                body={"values": rows_to_add}
            )
            response = request.execute()

            print(f"Added {len(rows_to_add)} new contacts to CRM sheet")
            print(f"New contacts: {', '.join(contacts_to_add)}")

            # Create a pretty table to display added contacts
            if contacts_to_add:
                table = PrettyTable()
                table.field_names = ["Email"]
                for email in contacts_to_add:
                    table.add_row([email])
                print("\nNew Contacts Added:")
                print(table)

            return {
                'success': True,
                'processed_count': len(meta_data),
                'added_count': len(rows_to_add),
                'added_contacts': contacts_to_add
            }
        else:
            print("No new contacts to add to CRM sheet")
            return {
                'success': True,
                'processed_count': len(meta_data),
                'added_count': 0,
                'added_contacts': []
            }

    except Exception as e:
        print(f"Error pulling contacts from Meta: {e}")
        return {'success': False, 'error': str(e)}

def pull_contacts_from_web():
    """
    Pull contacts from Web sheet and add them to CRM sheet if they don't already exist

    Returns:
        dict: Results with success status and counts of processed and added contacts
    """
    print(f"Pulling contacts from Web sheet to CRM")

    try:
        # Get the sheet service
        service = google_sheets.get_sheets_service()
        if not service:
            print("Failed to get Google Sheets service.")
            return {'success': False, 'error': 'Sheet service unavailable'}

        # Read all rows from the Web sheet
        web_data, web_headers = google_sheets.google_sheet_get_rows(LEADS_SPREADSHEET_ID, LEADS_WEB_SHEET_NAME)
        if web_data is None:
            print(f"Failed to read Web sheet")
            return {'success': False, 'error': 'Failed to read Web sheet'}

        print(f"Retrieved {len(web_data)} rows from Web sheet")

        # Read all rows from the CRM sheet
        crm_data, crm_headers = google_sheets.google_sheet_get_rows(CRM_SPREADSHEET_ID, CRM_SHEET_NAME)
        if crm_data is None:
            print(f"Failed to read CRM sheet {CRM_SHEET_NAME}")
            return {'success': False, 'error': 'Failed to read CRM sheet'}

        print(f"Retrieved {len(crm_data)} rows from CRM sheet")

        # Verify email column exists in both sheets
        if 'email' not in web_headers or 'email' not in crm_headers:
            print("Error: 'email' column not found in one or both sheets")
            return {'success': False, 'error': 'Email column missing'}

        # Create a set of existing emails in CRM for fast lookup
        existing_emails = {row['email'].lower().strip() for row in crm_data if 'email' in row and row['email'].strip()}

        # Create a set of existing phone numbers in CRM for fast lookup
        existing_phones = {
            row['phone_number'].replace(' ', '').replace('-', '').strip()
            for row in crm_data
            if 'phone_number' in row and row['phone_number']
        }

        # Prepare rows to add
        rows_to_add = []
        for contact in web_data:
            email = contact.get('email', '').strip().lower()
            phone = contact.get('phone', '').replace(' ', '').replace('-', '').strip()

            # Skip if email or phone already exists in CRM
            if email in existing_emails or phone in existing_phones:
                continue

            # Create a new row with empty cells
            new_row = [""] * len(crm_headers)

            # Map fields from Web sheet to CRM sheet
            new_row[crm_headers.index('platform')] = 'web'
            new_row[crm_headers.index('nome')] = contact.get('name', '')
            new_row[crm_headers.index('phone_number')] = contact.get('phone', '')
            new_row[crm_headers.index('email')] = contact.get('email', '')
            new_row[crm_headers.index('business_or_home_user?')] = 'business' if contact.get('business_owner', '').lower() == 'yes' else 'home'

            # Merge request_reason and request into notes
            request_reason = contact.get('request_reason', '')
            request = contact.get('request', '')
            new_row[crm_headers.index('notes')] = f"({request_reason}) {request}".strip()

            # Determine if user is business or home based on the business_owner field
            is_business = contact.get('business_owner', '').lower() == 'yes'

            # Set the "op" column with the appropriate code if it exists
            if 'op' in crm_headers:
                new_row[crm_headers.index('op')] = choose_operation(is_business)

            rows_to_add.append(new_row)

        # Add new rows to CRM sheet if any
        if rows_to_add:
            # Find the first empty row
            first_empty_row = len(crm_data) + 2  # +2 for header row and 1-indexed

            # Calculate end column letter properly for sheets with many columns
            def column_letter(col_index):
                """Convert a column number to a column letter (A, B, C, ..., Z, AA, AB, ...)"""
                letter = ""
                while col_index > 0:
                    col_index, remainder = divmod(col_index - 1, 26)
                    letter = chr(65 + remainder) + letter
                return letter

            # Prepare the range with proper column letters
            last_column_letter = column_letter(len(crm_headers))
            range_name = f"{CRM_SHEET_NAME}!A{first_empty_row}:{last_column_letter}{first_empty_row + len(rows_to_add) - 1}"

            # Update the sheet
            request = service.spreadsheets().values().update(
                spreadsheetId=CRM_SPREADSHEET_ID,
                range=range_name,
                valueInputOption="USER_ENTERED",
                body={"values": rows_to_add}
            )
            response = request.execute()

            print(f"Added {len(rows_to_add)} new contacts to CRM sheet")
            return {
                'success': True,
                'processed_count': len(web_data),
                'added_count': len(rows_to_add)
            }
        else:
            print("No new contacts to add to CRM sheet")
            return {
                'success': True,
                'processed_count': len(web_data),
                'added_count': 0
            }

    except Exception as e:
        print(f"Error pulling contacts from Web: {e}")
        return {'success': False, 'error': str(e)}

def main():
    """Main function to run the script"""
    print("Starting pull_contacts.py")
    print(f"Current date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    result = pull_contacts_from_meta()

    if result['success']:
        print(f"Added {result['added_count']} new contacts from META to CRM")
    else:
        print(f"Failed to pull contacts: {result.get('error', 'Unknown error')}")

    result = pull_contacts_from_web()

    if result['success']:
        print(f"Added {result['added_count']} new contacts from WEB to CRM")
    else:
        print(f"Failed to pull contacts: {result.get('error', 'Unknown error')}")

    print("Completed pull_contacts.py")

if __name__ == "__main__":
    main()