import re
import datetime
import os
import sys
import json
import argparse
from email.utils import parsedate_to_datetime
from prettytable import PrettyTable
from collections import defaultdict

# Add parent directory to path to import utils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils import google_credentials
from utils import google_sheets
from utils import gmail_process
from utils.CONFIG import CRM_SPREADSHEET_ID, CRM_SHEET_NAME

# Define your company's email domains here
your_domains = [
    "tabulatouch.com",
    "tabulatouch.eu",
    # Add more domains as needed
]

# Global variable to control saving of threads
SAVE_THREADS = False  # Set to False to disable saving
THREADS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "threads") # Corrected path relative to this file's parent's parent

def extract_email_address(email_string):
    """Extract just the email address from a string like 'Name <<EMAIL>>'"""
    match = re.search(r'<([^>]+)>', email_string)
    if match:
        return match.group(1).lower()

    # If no angle brackets, try to get the email directly
    match = re.search(r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', email_string)
    if match:
        return match.group(1).lower()

    return email_string.lower()

def format_date(date_string):
    """Convert various date formats to a standardized format"""
    try:
        # Parse the date string to a datetime object
        dt = parsedate_to_datetime(date_string)
        # Format it as a readable string
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except Exception:
        # Return the original if parsing fails
        return date_string

def determine_status(sender_email, recipient_email, your_email_domains):
    """
    Determine if the lead is in an "open" or "wait" state
    based on the direction of the email (who sent to whom)

    Args:
        sender_email: Email address of the sender
        recipient_email: Email address of the recipient
        your_email_domains: List of your company's email domains

    Returns:
        str: "open" if lead is waiting for your response, "wait" if you're waiting for their response
    """
    # Normalize emails for consistent comparison
    sender = sender_email.lower()
    recipient = recipient_email.lower()

    # Check if any of your domains are in the sender or recipient
    is_from_your_domain = any(domain.lower() in sender for domain in your_email_domains)
    is_to_your_domain = any(domain.lower() in recipient for domain in your_email_domains)

    # Determine status based on email direction
    if is_from_your_domain:
        # It's from your domain, so you sent to the lead (waiting for their response)
        return "wait"
    elif is_to_your_domain:
        # It's to your domain, so lead sent to you (needs your response)
        return "open"
    else:
        # If domain detection fails, fall back to a simpler check
        # This assumes the user's email is recipient when sending, sender when receiving
        if recipient.count('@') > sender.count('@'):
            # Multiple recipients suggests broadcast from you
            return "wait"
        else:
            # Otherwise assume it's from lead to you
            return "open"

def update_crm_sheet(results):
    """
    Update the CRM Google Sheet with the latest conversation data

    Args:
        results: List of dictionaries containing customer conversation data

    Returns:
        bool: True if sheet was updated successfully, False otherwise
    """
    print(f"Updating CRM sheet: {CRM_SHEET_NAME}")

    try:
        # Get the sheet service (needed after)
        service = google_sheets.get_sheets_service()
        if not service:
            print("Failed to get Google Sheets service.")
            return False

        # Read all rows from the sheet
        sheet_data, headers = google_sheets.google_sheet_get_rows(CRM_SPREADSHEET_ID, CRM_SHEET_NAME)
        if sheet_data is None:
            print(f"Failed to read sheet {CRM_SHEET_NAME}")
            return False

        print(f"Retrieved {len(sheet_data)} rows from CRM sheet")
        # print(f"Sheet headers: {headers}")

        # Find email column index and required column indexes
        if 'email' not in headers:
            print("Error: 'email' column not found in the CRM sheet")
            return False

        # Find the correct column indices for the fields we need to update
        last_activity_col = None
        mail_count_col = None
        crm_mail_col = None
        cust_activity_col = None

        for idx, header in enumerate(headers):
            header = header.lower()
            if 'last_activity' in header:
                last_activity_col = idx + 1  # Convert to 1-indexed
            elif 'mail_count' in header:
                mail_count_col = idx + 1
            elif 'crm_mail' in header:
                crm_mail_col = idx + 1
            elif 'cust_activity' in header:
                cust_activity_col = idx + 1

        # Verify we found all required columns
        if last_activity_col is None:
            print("Warning: 'last_activity' column not found, will skip updating this field")
        if mail_count_col is None:
            print("Warning: 'mail_count' column not found, will skip updating this field")
        if crm_mail_col is None:
            print("Warning: 'crm_mail' column not found, will skip updating this field")
        if cust_activity_col is None:
            print("Warning: 'cust_activity' column not found, will skip updating this field")

        # Find phone column index if it exists
        phone_col = None
        for idx, header in enumerate(headers):
            header = header.lower()
            if 'phone_number' in header:
                phone_col = idx + 1  # Convert to 1-indexed
                break

        if phone_col is None:
            print("Warning: 'phone_number' column not found, will skip updating this field")

        # Build email to row index mapping for quick lookups
        email_to_row = {}
        for i, row in enumerate(sheet_data):
            if 'email' in row and row['email'].strip():
                email_to_row[row['email'].lower().strip()] = i + 2  # +2 because row 1 is headers, and sheets are 1-indexed

        # Prepare batch updates
        batch_updates = []
        not_found_emails = []

        # Process each result
        for result in results:
            customer_email = result['customer_email'].lower().strip()

            if customer_email in email_to_row:
                row_index = email_to_row[customer_email]

                # Create Gmail search URL with customer email
                gmail_search_url = f"https://mail.google.com/mail/u/0/#search/{customer_email.replace('@', '%40')}"

                # Format the status column with hyperlink
                hyperlinked_status = f'=HYPERLINK("{gmail_search_url}"; "{result["status"]}")'

                # Update the specific cells for this customer (only if column was found)
                if last_activity_col:
                    # Convert the date to D/M/Y format for last_activity column
                    try:
                        # Try to parse date in the format "YYYY-MM-DD HH:MM:SS" first
                        if re.match(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', result['last_message_date']):
                            dt = datetime.datetime.strptime(result['last_message_date'], "%Y-%m-%d %H:%M:%S")
                        else:
                            # Fall back to parsedate_to_datetime for other formats
                            dt = parsedate_to_datetime(result['last_message_date'])

                        # Format it as D/M/Y
                        formatted_date = dt.strftime("%d/%m/%Y")
                    except Exception as e:
                        # If parsing fails, use the original date string
                        print(f"Date parsing error for {result['last_message_date']}: {e}")
                        formatted_date = result['last_message_date']

                    batch_updates.append({
                        'range': f'{CRM_SHEET_NAME}!{chr(64 + last_activity_col)}{row_index}',
                        'values': [[formatted_date]]
                    })

                if mail_count_col:
                    batch_updates.append({
                        'range': f'{CRM_SHEET_NAME}!{chr(64 + mail_count_col)}{row_index}',
                        'values': [[result['message_count']]]
                    })

                if crm_mail_col:
                    batch_updates.append({
                        'range': f'{CRM_SHEET_NAME}!{chr(64 + crm_mail_col)}{row_index}',
                        'values': [[hyperlinked_status]]
                    })

                # Update cust_activity column with count of customer-initiated messages
                if cust_activity_col and 'customer_message_count' in result:
                    # Only add non-zero values, leave blank if zero
                    if result['customer_message_count'] > 0:
                        batch_updates.append({
                            'range': f'{CRM_SHEET_NAME}!{chr(64 + cust_activity_col)}{row_index}',
                            'values': [[result['customer_message_count']]]
                        })
                    else:
                        # If count is zero, set to blank
                        batch_updates.append({
                            'range': f'{CRM_SHEET_NAME}!{chr(64 + cust_activity_col)}{row_index}',
                            'values': [['']]
                        })

                # Update phone column with WhatsApp link if available
                if phone_col:
                    # Get the current phone value from the sheet
                    current_phone = None
                    for key, value in sheet_data[row_index - 2].items():  # -2 to adjust back to 0-indexed and account for header
                        if key.lower() in ['phone_number', 'mobile', 'tel']:
                            current_phone = value
                            break

                    # Only process phone if it exists and is not empty
                    if current_phone and current_phone.strip():
                        # Format phone number: strip spaces and handle p:+ prefix
                        formatted_phone = current_phone.strip().replace(' ', '')

                        # Handle p:+ prefix if it exists
                        if formatted_phone.startswith('p:+'):
                            # Remove the p: but keep the +
                            formatted_phone = formatted_phone[2:]
                        elif not formatted_phone.startswith('+'):
                            # If no plus, add it (assuming international format)
                            formatted_phone = '+' + formatted_phone

                        # Create WhatsApp link - preserve the + sign in the phone number
                        whatsapp_url = f"https://wa.me/{formatted_phone}"  # Keep the + sign for the URL
                        hyperlinked_phone = f'=HYPERLINK("{whatsapp_url}"; "{current_phone}")'

                        batch_updates.append({
                            'range': f'{CRM_SHEET_NAME}!{chr(64 + phone_col)}{row_index}',
                            'values': [[hyperlinked_phone]]
                        })
                    # No else needed - if phone is empty, we simply don't update it
            else:
                not_found_emails.append(customer_email)

        # Report on emails not found
        '''
        if not_found_emails:
            print(f"Warning: {len(not_found_emails)} email(s) not found in CRM sheet:")
            for email in not_found_emails:
                print(f"  - {email}")
        '''

        # Execute batch update if there are updates to make
        if batch_updates:
            body = {
                'valueInputOption': 'USER_ENTERED',
                'data': batch_updates
            }

            response = service.spreadsheets().values().batchUpdate(
                spreadsheetId=CRM_SPREADSHEET_ID,
                body=body
            ).execute()

            print(f"Updated {len(batch_updates)} cells in CRM sheet")
            return True
        else:
            print("No updates to make in CRM sheet")
            return True

    except Exception as e:
        print(f"Error updating CRM sheet: {e}")
        return False

def clean_message_body(text_body):
    """
    Removes quoted text and signatures from an email body.
    This function tries to identify the actual new message content.
    """
    if not text_body:
        return ""

    cleaned_lines = []
    original_lines = text_body.splitlines()

    # Patterns that usually indicate the start of a quoted reply or forwarded message
    on_wrote_pattern = re.compile(r"^\s*On\s.+?\s(wrote|said|écrit|scrisse|escribió):", re.IGNORECASE)
    forward_divider_pattern = re.compile(r"^\s*---[- ]*(Original|Forwarded) Message[- ]*---", re.IGNORECASE)
    # Pattern for signature lines like "-- " or "--"
    signature_pattern = re.compile(r"^\s*--\s*(\s.*)?$")
    # Generic reply lines like "From: ... Sent: ... To: ... Subject: ..."
    reply_header_pattern = re.compile(r"^\s*(From|Sent|To|Subject|Date):", re.IGNORECASE)

    for line_content in original_lines:
        stripped_content = line_content.strip()

        # 1. If it's a line starting with '>', it's part of a quote.
        if stripped_content.startswith(">"):
            continue

        # 2. If it matches "On ... wrote:", assume new message content has ended.
        if on_wrote_pattern.match(line_content):
            break

        # 3. If it matches "--- Original/Forwarded Message ---", new message content has ended.
        if forward_divider_pattern.match(line_content):
            break

        # 4. If it matches a signature separator like "-- ", new message content has ended.
        if signature_pattern.match(line_content):
            break

        # 5. If it matches common reply headers, assume new message content has ended.
        if reply_header_pattern.match(line_content):
            # This is a bit aggressive; consider if previous lines were substantial
            if len(cleaned_lines) > 2: # Check if we have some content already
                 # Check if the last few lines were also headers or very short
                recent_lines_look_like_header = True
                for i in range(1, min(4, len(cleaned_lines) + 1)):
                    if len(cleaned_lines[-i].strip()) > 10 and not reply_header_pattern.match(cleaned_lines[-i]):
                        recent_lines_look_like_header = False
                        break
                if recent_lines_look_like_header:
                    break # Stop if it looks like a block of headers

        cleaned_lines.append(line_content)

    return "\\n".join(cleaned_lines).strip()

def process_messages(messages_array):
    """
    Process multiple CRM emails at once in batch mode

    Args:
        messages_array: List of message dictionaries with the following keys:
            - message_id: Unique message ID
            - date: Email date string
            - sender: Email sender
            - recipient: Email recipient
            - subject: Email subject
            - message_body_html: HTML body content
            - message_body_text: Plain text body content

    Returns:
        dict: Processing results with success status and number of processed messages
    """
    # Track processed messages
    processed_count = 0

    # Dictionary to store conversations grouped by customer email
    customer_conversations = defaultdict(list)

    if SAVE_THREADS:
        # Ensure THREADS_DIR is an absolute path or correctly relative
        # Assuming THREADS_DIR is defined at the top level of the script
        # and os.path.join is used correctly there.
        if not os.path.exists(THREADS_DIR):
            try:
                os.makedirs(THREADS_DIR)
                print(f"Created directory: {THREADS_DIR}")
            except OSError as e:
                print(f"Error creating directory {THREADS_DIR}: {e}")
                # Optionally, handle the error e.g., by disabling SAVE_THREADS for this run
                # global SAVE_THREADS # If you need to modify the global var
                # SAVE_THREADS = False

    # Process and group all messages by customer email
    for message in messages_array:
        # Extract email addresses
        sender_email = extract_email_address(message['sender'])
        recipient_email = extract_email_address(message['recipient'])

        # Format date consistently
        formatted_date = format_date(message['date'])

        try:
            parsed_date = parsedate_to_datetime(message['date'])
            # Ensure parsed_date is timezone-aware
            if parsed_date.tzinfo is None:
                # If the parsed date has no timezone, make it timezone-aware by adding UTC
                parsed_date = parsed_date.replace(tzinfo=datetime.timezone.utc)
        except Exception:
            # If date parsing fails, use current time as fallback with timezone info
            # Create a timezone-aware datetime to match what parsedate_to_datetime returns
            parsed_date = datetime.datetime.now(datetime.timezone.utc)

        # Check if sender is from your company
        is_sender_company = any(domain.lower() in sender_email.lower() for domain in your_domains)

        # Check if recipient is from your company
        is_recipient_company = any(domain.lower() in recipient_email.lower() for domain in your_domains)

        # Skip internal emails (both sender and recipient are company domains)
        if is_sender_company and is_recipient_company:
            continue

        # Determine customer email (the non-company email)
        if is_sender_company:
            # Sent from your company to customer
            customer_email = recipient_email
            is_from_customer = False
        else:
            # Sent from customer to your company
            customer_email = sender_email
            is_from_customer = True

        # Add message to the appropriate customer conversation
        customer_conversations[customer_email].append({
            'message_id': message['message_id'],
            'date': formatted_date,
            'parsed_date': parsed_date,
            'sender': sender_email,
            'recipient': recipient_email,
            'is_from_customer': is_from_customer,
            'direction': "Customer → Company" if is_from_customer else "Company → Customer",
            'body_text': message.get('message_body_text', '') # Ensure body_text is included
        })

        processed_count += 1

    # Analyze each customer's conversation to find the most recent message and its status
    results = []

    for customer_email, messages in customer_conversations.items():
        # Sort messages by date (chronologically for saving, most recent last for analysis)
        sorted_messages = sorted(messages, key=lambda x: x['parsed_date'])

        if sorted_messages:
            # Get the most recent message
            last_message = sorted_messages[-1]

            # Determine status based on the last message direction
            # If the last message is from customer to us, status is "customer" (we need to respond)
            # If the last message is from us to customer, status is "company" (waiting for customer response)
            status = "CUSTOMER" if last_message['is_from_customer'] else "company"

            # Count messages sent by customer
            customer_message_count = sum(1 for msg in sorted_messages if msg['is_from_customer'])

            results.append({
                'customer_email': customer_email,
                'last_message_date': last_message['date'],
                'status': status,
                'message_count': len(sorted_messages),
                'customer_message_count': customer_message_count,
                'message_id': last_message['message_id'],
                'last_direction': last_message['direction']
            })

            if SAVE_THREADS and customer_email and os.path.exists(THREADS_DIR):
                # Sanitize email to be a valid filename (replace potentially problematic characters)
                # Allows a-z, A-Z, 0-9, ., _, - and replaces others with _
                safe_filename_base = re.sub(r'[^a-zA-Z0-9._-]', '_', customer_email)
                # Ensure the filename is not too long
                max_len = 200 # Max length for filename part
                if len(safe_filename_base) > max_len:
                    safe_filename_base = safe_filename_base[:max_len]

                filepath = os.path.join(THREADS_DIR, f"{safe_filename_base}.json") # Changed to .json

                thread_data = {"mail_messages": []}

                try:
                    for idx, msg_data in enumerate(sorted_messages): # Iterate chronologically
                        cleaned_body = clean_message_body(msg_data.get('body_text', '')) # Use .get for safety

                        # Skip writing if the cleaned body is empty
                        if not cleaned_body.strip():
                            continue

                        user_type = "customer" if msg_data['is_from_customer'] else "company"

                        thread_data["mail_messages"].append({
                            "idx": idx + 1, # 1-based index as per user request
                            "user": user_type,
                            "text": cleaned_body
                        })

                    # Only save if there are messages to save
                    if thread_data["mail_messages"]:
                        with open(filepath, 'w', encoding='utf-8') as f:
                            json.dump(thread_data, f, ensure_ascii=False, indent=4) # Save as JSON with indent
                        # print(f"Saved JSON thread for {customer_email} to {filepath}")
                except Exception as e:
                    print(f"Error saving JSON thread for {customer_email} to {filepath}: {e}")

    # Create and display a pretty table with the results
    table = PrettyTable()
    table.field_names = ["Customer Email", "Last Message Date", "Status"]

    for result in results:
        table.add_row([
            result['customer_email'],
            result['last_message_date'],
            result['status']
        ])

    # Sort by status (open first) and then by date (most recent first)
    table.sortby = "Status"
    table.reversesort = False

    # print("\nCustomer Conversation Summary:")
    # print(table)

    # Update the CRM Google Sheet with the results
    update_success = update_crm_sheet(results)
    if not update_success:
        print("Warning: Failed to update CRM sheet")

    return {
        'success': True,
        'processed_count': processed_count,
        'customer_count': len(results),
        'results': results
    }


def main():
    """Command-line entry point for CRM email processing."""
    parser = argparse.ArgumentParser(description="Process Gmail messages for CRM updates")
    parser.add_argument('--sender', help='Email address of the sender to search for')
    parser.add_argument('--subject', help='Subject line to search for (contains)')
    parser.add_argument('--label', help='Gmail label to search for')
    parser.add_argument('--reset', action='store_true',
                      help='Reset search state (process all matching emails)')
    parser.add_argument('--batch', action='store_true', default=True,
                      help='Process messages in batch mode (default: True)')
    parser.add_argument('--state', help='Custom state file name to use instead of auto-generated one')

    args = parser.parse_args()

    # Check that at least one filter is provided
    if not args.sender and not args.subject and not args.label:
        parser.error("At least one filter (--sender, --subject, or --label) must be provided")

    # Reset state if requested
    if args.reset:
        if args.state:
            # Use custom state filename if provided
            state_file = os.path.join(gmail_process.STATE_DIR, f"{args.state}.json")
            if os.path.exists(state_file):
                os.remove(state_file)
                print(f"Reset state file: {state_file}")
        else:
            # Use auto-generated filename
            state_file = os.path.join(gmail_process.STATE_DIR,
                                    gmail_process.get_state_filename("sargame_CRM", args.sender, args.subject, args.label))
            if os.path.exists(state_file):
                os.remove(state_file)
                print(f"Reset state for CRM processor and filters: sender='{args.sender or ''}', subject='{args.subject or ''}', label='{args.label or ''}'")

    # Get Gmail API service using utility module
    service = google_credentials.get_gmail_service()

    if service is None:
        print("Failed to get Gmail service. Check your credentials and try again.")
        return

    # Load previous state if custom_state is provided
    state = gmail_process.load_previous_state("sargame_CRM", args.sender, args.subject, args.label, args.state)
    last_timestamp = state.get('last_timestamp')

    # Get new messages (list of message IDs)
    message_ids = gmail_process.get_email_messages(service, args.sender, args.subject, args.label, last_timestamp)

    if not message_ids:
        print("No new messages found")
        return

    # Fetch full message content for all IDs
    full_messages = []
    for msg_ref in message_ids:
        full_msg = gmail_process.get_message_content(service, msg_ref['id'])
        if full_msg:
            full_messages.append(full_msg)
        else:
            print(f"Skipping message {msg_ref['id']} due to fetch error.")

    # Sort messages by internalDate (oldest first)
    full_messages.sort(key=lambda x: int(x.get('internalDate', 0)))

    print(f"Processing {len(full_messages)} messages in chronological order...")

    # Prepare a list of formatted messages for batch processing
    formatted_messages = []
    current_timestamp = None

    for full_msg in full_messages:
        # Extract message body and headers
        body = gmail_process.get_message_body(full_msg)
        headers = gmail_process.get_message_headers(full_msg)

        # Extract timestamp from the full message
        msg_timestamp = int(full_msg.get('internalDate', 0))

        # Update the latest timestamp
        if msg_timestamp:
            current_timestamp = max(current_timestamp or 0, msg_timestamp)

        # Extract required data for the batch processing
        subject = headers.get('subject', '')
        from_email = headers.get('from', '')
        to_email = headers.get('to', '')

        # Get date from headers or message timestamp
        date_header = headers.get('date', '')
        if date_header:
            date = date_header
        else:
            timestamp = msg_timestamp
            if timestamp:
                date = datetime.datetime.fromtimestamp(int(timestamp)/1000).strftime('%a, %d %b %Y %H:%M:%S %z')
            else:
                date = datetime.datetime.now().strftime('%a, %d %b %Y %H:%M:%S %z')

        # Get HTML and plain text content
        message_body_html = body.get('html', '') if body else ''
        message_body_text = body.get('plain', '') if body else ''

        # Add formatted message to the list
        formatted_messages.append({
            'message_id': full_msg['id'],
            'date': date,
            'sender': from_email,
            'recipient': to_email,
            'subject': subject,
            'message_body_html': message_body_html,
            'message_body_text': message_body_text
        })

    # Process all messages at once
    processed_count = 0
    try:
        result = process_messages(formatted_messages)
        if result.get('success'):
            processed_count = result.get('processed_count', len(formatted_messages))
        else:
            print(f"Batch processing failed: {result.get('error', 'Unknown error')}")
    except Exception as e:
        print(f"Error in batch processing: {e}")

    # Update and save state if we processed any messages and custom_state is provided
    if processed_count > 0:
        # Use the timestamp of the *last* processed message (most recent)
        if current_timestamp:
            # Only update state if the new timestamp is greater than the last saved one
            if last_timestamp is None or current_timestamp > last_timestamp:
                state['last_timestamp'] = current_timestamp
                # Remove the processed_ids field if it exists from previous version
                if 'processed_ids' in state:
                    del state['processed_ids']
                gmail_process.save_state("sargame_CRM", args.sender, args.subject, args.label, state, args.state)
            else:
                print(f"Processed {processed_count} messages, but no newer timestamp found ({current_timestamp} <= {last_timestamp}). State not updated.")
        else:
            print(f"Processed {processed_count} new messages, but couldn't determine timestamp. State not updated.")
    else:
        print("No new messages were processed.")


if __name__ == "__main__":
    main()