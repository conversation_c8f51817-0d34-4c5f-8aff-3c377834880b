#!/usr/bin/env python3
# filepath: /Users/<USER>/GIT/tabula.scripts/TABULA/tasks/mail_facadesignage_downloaders.py

import sys
import os
from datetime import datetime

# Add the parent directory to sys.path to import utils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.azure import connect_sql_fcd, get_fcd_download_emails

def main():
    """
    Main function to connect to the database and retrieve emails.
    """
    print("Starting Azure SQL database connection test...")
    
    try:
        # Get emails from the last month using the utility function
        print("Connecting to Azure SQL database...")
        emails = get_fcd_download_emails(days=30)
        
        print(f"\nSuccessfully retrieved data.")
        print(f"Found {len(emails)} unique email addresses from the last month:")
        for i, email in enumerate(emails):
            print(f"{i+1}. {email}")
            
    except Exception as e:
        print(f"Error in main execution: {e}")

if __name__ == "__main__":
    main()
