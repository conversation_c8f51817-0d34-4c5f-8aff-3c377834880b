#!/usr/bin/env python3
"""
WhatsApp Webhook Receiver

This script starts a webhook server for receiving WhatsApp messages using the Waha API.
Messages received will be logged to a CSV file.
"""

import sys
import signal
import os
import argparse

# Add the parent directory to sys.path to import waha module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from utils import waha

def signal_handler(sig, frame):
    """Handle interrupt signals to gracefully exit the script"""
    print('\nStopping WhatsApp webhook server...')
    sys.exit(0)

def main():
    """Main function to start the WhatsApp webhook server"""
    parser = argparse.ArgumentParser(description='Start a WhatsApp webhook server for Waha API')
    parser.add_argument('--host', default='0.0.0.0', help='Hostname to bind the webhook server to (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=50000, help='Port to bind the webhook server to (default: 5000)')
    parser.add_argument('--endpoint', default='/waha-webhook', help='Endpoint path for the webhook (default: /waha-webhook)')
    parser.add_argument('--secret-key', help='Secret key for verifying HMAC signatures')
    parser.add_argument('--events', nargs='+', default=['message', 'message.any'], help='Events to listen for (default: message message.any)')
    parser.add_argument('--debug', action='store_true', help='Run Flask in debug mode')
    args = parser.parse_args()
    
    print(f"Starting WhatsApp webhook server on http://{args.host}:{args.port}{args.endpoint}")
    print(f"Listening for events: {', '.join(args.events)}")
    print("Messages will be saved to TABULA/logs/whatsapp_messages.csv")
    print("Press Ctrl+C to stop")
    
    # Register signal handler for graceful exit
    signal.signal(signal.SIGINT, signal_handler)
    
    # Start the webhook server (this is a blocking call)
    waha.start_webhook_server(
        host=args.host,
        port=args.port,
        endpoint=args.endpoint,
        hmac_secret_key=args.secret_key,
        allowed_events=args.events,
        debug=args.debug
    )

if __name__ == "__main__":
    main()