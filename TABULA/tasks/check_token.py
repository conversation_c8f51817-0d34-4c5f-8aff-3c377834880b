#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to check if the Google API token is still valid.

This script checks the validity of the Google API token and returns
appropriate exit codes:
- 0: Token is valid
- 1: Token is invalid

If the token is invalid, it sends an email notification (max once per day).
"""

import os
import sys
import argparse
import json
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# Add the parent directory to the path to import from utils
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from utils.google_credentials import get_google_credentials
from utils.gmail_send import create_mail, create_html_body

# Constants
TOKEN_FILE = os.path.join(parent_dir, "token", "token.json")
LOG_FILE = os.path.join(parent_dir, "logs", "check_token.txt")
NOTIFICATION_EMAIL = "<EMAIL>"

def is_token_valid():
    """
    Check if the Google API token is valid.
    
    Returns:
        tuple: (is_valid, error_message)
            - is_valid (bool): True if token is valid, False otherwise
            - error_message (str): Error message if token is invalid, None otherwise
    """
    try:
        # Attempt to get credentials without triggering the OAuth flow
        creds = get_google_credentials(token_file=TOKEN_FILE, authflow=False)
        
        # Check if credentials were obtained and are valid
        if creds and creds.valid:
            return True, None
        else:
            if creds and creds.expired:
                return False, "Token has expired"
            else:
                return False, "Token is invalid or missing"
    except Exception as e:
        return False, f"Error checking token: {str(e)}"

def should_send_notification():
    """
    Check if a notification should be sent based on the last notification timestamp.
    
    Returns:
        bool: True if notification should be sent (last one >24h ago or never sent)
    """
    try:
        # Check if log file exists
        if not os.path.exists(LOG_FILE):
            return True
        
        # Read the last notification timestamp
        with open(LOG_FILE, 'r') as f:
            last_notification_str = f.read().strip()
            
        # Parse the timestamp
        last_notification = datetime.fromisoformat(last_notification_str)
        
        # Check if at least 24 hours have passed since the last notification
        now = datetime.now()
        return (now - last_notification) > timedelta(hours=24)
    
    except Exception as e:
        print(f"Error checking notification timestamp: {e}")
        # If there's any error, default to sending notification
        return True

def update_notification_timestamp():
    """
    Update the timestamp of the last sent notification.
    """
    try:
        # Ensure the logs directory exists
        os.makedirs(os.path.dirname(LOG_FILE), exist_ok=True)
        
        # Write current timestamp to the log file
        with open(LOG_FILE, 'w') as f:
            f.write(datetime.now().isoformat())
    
    except Exception as e:
        print(f"Error updating notification timestamp: {e}")

def send_notification_email(error_message):
    """
    Send a notification email about the invalid token using SMTP.
    
    Args:
        error_message (str): The error message about why the token is invalid
        
    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    try:
        # Create email content
        subject = "CRM: google token is invalid please refresh"
        html_content = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; padding: 20px; }}
                .warning {{ color: #D8000C; background-color: #FFBABA; padding: 15px; border-radius: 5px; }}
                .info {{ margin-top: 20px; }}
            </style>
        </head>
        <body>
            <h2>Google API Token Alert</h2>
            
            <div class="warning">
                <h3>Token Invalid</h3>
                <p>The Google API token used by the CRM system is currently invalid.</p>
                <p><strong>Error details:</strong> {error_message}</p>
            </div>                       
            
            <p>This is an automated message from the CRM token validation system.</p>
            <p><small>Sent at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</small></p>
        </body>
        </html>
        """
        
        plain_text = f"""
Google API Token Alert

The Google API token used by the CRM system is currently invalid.
Error details: {error_message}

Action Required:
Please refresh the token by running the refresh_token.py script:
cd {parent_dir}/tasks && python3 refresh_token.py

This is an automated message from the CRM token validation system.
Sent at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        try:
            # Import the send_mail function from SMTP utility
            from utils.smtp import send_mail
            
            # Send using SMTP since Google token is invalid
            result = send_mail(
                sender_email="S-ARGAME CRM <<EMAIL>>",  # Using SMTP_USER from smtp.py
                recipient_email=NOTIFICATION_EMAIL,
                subject=subject,
                message_body=html_content,
                is_html=True
            )
            
            if result['status'] == 'success':
                print("Notification email sent successfully via SMTP")
                return True
            else:
                print(f"SMTP sending failed: {result['message']}")
                return False
                
        except Exception as e:
            print(f"Failed to send notification email via SMTP: {e}")
            
            # As a fallback, try to create a minimal local email file for debugging
            try:
                debug_file = os.path.join(parent_dir, "logs", "token_alert_debug.html")
                with open(debug_file, 'w') as f:
                    f.write(html_content)
                print(f"Saved debug email to {debug_file}")
            except Exception:
                pass
                
            return False
            
    except Exception as e:
        print(f"Error preparing notification email: {e}")
        return False

def main():
    """
    Main function to check the token and send notification if needed.
    """
    parser = argparse.ArgumentParser(description="Check if the Google API token is valid.")
    parser.add_argument("--silent", action="store_true", help="Suppress console output")
    args = parser.parse_args()
    
    # Check token validity
    is_valid, error_message = is_token_valid()
    
    if not args.silent:
        if is_valid:
            print("Google Auth Token is valid.")
        else:
            print(f"Google Auth Token is invalid: {error_message}")
    
    # Handle invalid token case
    if not is_valid:
        # Check if notification should be sent
        if should_send_notification():
            # Send notification
            if not args.silent:
                print("Sending notification email...")
            
            success = send_notification_email(error_message)
            
            if success:
                update_notification_timestamp()
                if not args.silent:
                    print("Notification email sent.")
            elif not args.silent:
                print("Failed to send notification email.")
        elif not args.silent:
            print("Notification already sent within the last 24 hours.")
        
        # Return exit code 1 for invalid token
        sys.exit(1)
    
    # Return exit code 0 for valid token
    sys.exit(0)

if __name__ == "__main__":
    main()
