#!/usr/bin/env python3
# filepath: /Users/<USER>/GIT/tabula.scripts/TABULA/tasks/get_crm_sqlite.py
"""
Script to convert the CRM Google Sheet into a SQLite database file.
Uses the google_sheets module to access the sheet and transforms it into SQLite.
Now with support for incremental updates - only changed and new records are updated.
"""

import os
import sys
import sqlite3
from pathlib import Path
import shutil

# Add parent directory to path to import from utils
parent_dir = str(Path(__file__).resolve().parent.parent)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from utils import google_sheets, sqlite
from utils.CONFIG import CRM_SPREADSHEET_ID, CRM_SHEET_NAME


def main():
    """
    Main function to convert the CRM Google Sheet to SQLite.
    Now supports incremental updates.
    """
    # Define output paths directly
    sqlite_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'sqlite')
    os.makedirs(sqlite_dir, exist_ok=True)

    # Target database path (existing or to be created)
    sqlite_path = os.path.join(sqlite_dir, 'crm_latest.sqlite')

    # Temporary database path
    temp_sqlite_path = os.path.join(sqlite_dir, 'crm_temp.sqlite')

    # Get column types from schema file
    column_types = sqlite.get_column_types_from_schema()

    # Check if we need to create a new database or update existing one
    create_new = not os.path.exists(sqlite_path)

    print(f"Converting CRM sheet '{CRM_SHEET_NAME}' to temporary SQLite database...")

    # Always create a fresh temporary database from the sheet
    temp_success = google_sheets.google_sheet_to_sqlite(
        spreadsheet_id=CRM_SPREADSHEET_ID,
        sheet_name=CRM_SHEET_NAME,
        sqlite_path=temp_sqlite_path,
        table_name='crm',
        column_types=column_types
    )

    if not temp_success:
        print("Failed to convert CRM sheet to temporary SQLite database.")
        # Clean up temp file if it exists
        if os.path.exists(temp_sqlite_path):
            os.remove(temp_sqlite_path)
        return False

    # If this is our first run, just rename the temp file to the target
    if create_new:
        print("No existing database found. Creating new one...")
        shutil.move(temp_sqlite_path, sqlite_path)
        print(f"Successfully created new SQLite database at: {sqlite_path}")

        # Quick verification
        try:
            conn = sqlite3.connect(sqlite_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM crm")
            count = cursor.fetchone()[0]
            print(f"Database contains {count} records")

            cursor.execute("PRAGMA table_info(crm)")
            columns = [col[1] for col in cursor.fetchall()]
            print(f"Found columns: {', '.join(columns)}")
            conn.close()
        except Exception as e:
            print(f"Warning: Error while verifying database: {e}")

        return True
    else:
        # For subsequent runs, sync temp database with existing one
        print("Existing database found. Synchronizing changes...")
        total_changes, new_records, updated_records = sqlite.sync_databases(sqlite_path, temp_sqlite_path)

        # Display changes in a pretty table format
        sqlite.display_changes_table(new_records, updated_records)

        print(f"\nSynchronization complete. Total changes: {total_changes}")
        print(f"- New records: {len(new_records)}")
        print(f"- Updated records: {len(updated_records)}")

        # Clean up the temporary database
        if os.path.exists(temp_sqlite_path):
            os.remove(temp_sqlite_path)
            print("Temporary database cleaned up.")

        return True

if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
