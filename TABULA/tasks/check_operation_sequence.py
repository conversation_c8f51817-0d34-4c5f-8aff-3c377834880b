#!/usr/bin/env python3
# Script to check current CRM customers status to add new operations (mails) in the funnel sequence
# Always check the CRM schema in /TABULA/design/crm_sheet_columns.txt
import os
import sys
import argparse
from prettytable import PrettyTable
import smtplib
from email.mime.multipart import MIMEMult<PERSON>art
from email.mime.text import MIMEText
from datetime import datetime
from datetime import datetime, timedelta
import pandas as pd

import base64
from googleapiclient.errors import HttpError
from email.mime.text import MIMEText

# Add parent directory to path to import utils
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from utils import google_sheets
from utils.google_credentials import get_gmail_service
from utils.CONFIG import CRM_SPREADSHEET_ID, CRM_SHEET_NAME

def check_operation_sequence():

    try:
        # Load CRM data into a dataframe with unified cell metadata
        df = google_sheets.google_sheet_to_dataframe_with_metadata(CRM_SPREADSHEET_ID, CRM_SHEET_NAME)
        if df is None or df.empty:
            return {'success': False, 'error': 'Failed to load CRM data or no data available'}

        # Get customers (rows) that are considered "inactive", meaning that
        # - their last_activity is older than 5 days
        # - their mail_count is less than 3
        # - their cust_activity is null
        # - their wa_activity is null
        # - their crm_mail is "company"



        # Get current date
        current_date = datetime.now()
        # Calculate date 5 days ago
        five_days_ago = current_date - timedelta(days=5)

        # Convert last_activity to datetime if it's not already
        df['last_activity'] = pd.to_datetime(df['last_activity'], errors='coerce')

        # Print the first few last_activity values for debugging
        print("Sample last_activity values after conversion:")
        print(df['last_activity'].head())

        # For date comparison, ensure we're only comparing dates (not times)
        df['last_activity_date'] = df['last_activity'].dt.date
        five_days_ago_date = five_days_ago.date()
        print(f"Five days ago date: {five_days_ago_date}")

        # Convert mail_count to numeric
        df['mail_count'] = pd.to_numeric(df['mail_count'], errors='coerce')

        # Filter for inactive customers
        inactive_customers = df[
            (df['last_activity'] < five_days_ago) &
            (df['mail_count'] < 3) &
            ((df['cust_activity'].isnull()) | (df['cust_activity'] == '') | (df['cust_activity'] == 0)) &
            ((df['wa_activity'].isnull()) | (df['wa_activity'] == '') | (df['wa_activity'] == 0)) &
            (df['crm_mail'] == "company")
        ]

        # Print debugging info
        print(f"Total rows in dataframe: {len(df)}")
        print(f"Rows after last_activity filter: {len(df[df['last_activity'] < five_days_ago])}")
        print(f"Rows after mail_count filter: {len(df[(df['last_activity'] < five_days_ago) & (df['mail_count'] < 3)])}")
        print(f"Rows after cust_activity filter: {len(df[(df['last_activity'] < five_days_ago) & (df['mail_count'] < 3) & ((df['cust_activity'].isnull()) | (df['cust_activity'] == '') | (df['cust_activity'] == 0))])}")
        print(f"Rows after wa_activity filter: {len(df[(df['last_activity'] < five_days_ago) & (df['mail_count'] < 3) & ((df['cust_activity'].isnull()) | (df['cust_activity'] == '') | (df['cust_activity'] == 0)) & ((df['wa_activity'].isnull()) | (df['wa_activity'] == '') | (df['wa_activity'] == 0))])}")

        # Create a pretty table to display inactive customers
        table = PrettyTable()

        # Define columns to show in the table
        display_columns = ['company_name', 'contact_name', 'last_activity', 'mail_count', 'crm_mail']

        # Add fields to the table
        table.field_names = display_columns

        # Add rows to the table
        for _, row in inactive_customers.iterrows():
            table_row = [row[col] for col in display_columns]
            table.add_row(table_row)

        return {
            'success': True,
            'inactive_customers': inactive_customers,
            'table': table,
            'count': len(inactive_customers)
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def main():
    print("Starting check_operation_sequence.py")

    result = check_operation_sequence()

    if result['success']:
        print(f"Found {result['count']} inactive customers:")
        print(result['table'])
        print("Check operation sequence completed successfully.")
    else:
        print(f"Failed: {result.get('error', 'Unknown error')}")

    print("Completed check_operation_sequence.py")

if __name__ == "__main__":
    main()