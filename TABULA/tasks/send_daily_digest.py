#!/usr/bin/env python3
# Script to send a daily digest to an administrative email about the status of the leads
import os
import sys
import argparse
from prettytable import PrettyTable
import smtplib
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText
from datetime import datetime, timedelta

import base64
from googleapiclient.errors import HttpError
from email.mime.text import MIMEText

# Add parent directory to path to import utils
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from utils import google_sheets
from utils.google_credentials import get_gmail_service
# Import needed function from gmail_send.py
from utils.gmail_send import create_mail, create_html_body
from utils.CONFIG import CRM_SPREADSHEET_ID, CRM_SHEET_NAME

DIGEST_EMAIL = "<EMAIL>"
# File to store the last notification timestamp
LOG_FILE = os.path.join(parent_dir, "logs", "send_daily_digest.txt")

def should_send_digest():
    """
    Check if a digest should be sent based on the last digest timestamp.

    Returns:
        bool: True if digest should be sent (last one >24h ago or never sent)
    """
    try:
        # Check if log file exists
        if not os.path.exists(LOG_FILE):
            return True

        # Read the last notification timestamp
        with open(LOG_FILE, 'r') as f:
            last_digest_str = f.read().strip()

        # Parse the timestamp
        last_digest = datetime.fromisoformat(last_digest_str)

        # Check if at least 24 hours have passed since the last digest
        now = datetime.now()
        return (now - last_digest) > timedelta(hours=24)

    except Exception as e:
        print(f"Error checking digest timestamp: {e}")
        # If there's any error, default to sending digest
        return True

def update_digest_timestamp():
    """
    Update the timestamp of the last sent digest.
    """
    try:
        # Ensure the logs directory exists
        os.makedirs(os.path.dirname(LOG_FILE), exist_ok=True)

        # Write current timestamp to the log file
        with open(LOG_FILE, 'w') as f:
            f.write(datetime.now().isoformat())

    except Exception as e:
        print(f"Error updating digest timestamp: {e}")

def send_daily_digest():
    """
    Generates and sends a daily digest email with CRM customer statistics and hot leads.

    Returns:
        dict: Result containing success status and message/error
    """
    try:
        # Load CRM data into a dataframe with unified cell metadata
        df = google_sheets.google_sheet_to_dataframe_with_metadata(CRM_SPREADSHEET_ID, CRM_SHEET_NAME)
        if df is None or df.empty:
            return {'success': False, 'error': 'Failed to load CRM data or no data available'}

        # Create a dictionary to store extracted hyperlinks from formulas
        email_hyperlinks = {}

        # Get values DataFrame for standard operations
        values_df = df.get_values_dataframe()

        # Extract hyperlinks from crm_mail column formulas
        for idx, row in df.iterrows():
            if 'crm_mail' in df.columns:
                # Get formula from the cell
                formula = row['crm_mail']['formula']

                # Extract hyperlink URL from HYPERLINK formula if it exists
                # Format: =HYPERLINK("https://url"; "CUSTOMER")
                if isinstance(formula, str) and 'HYPERLINK(' in formula:
                    try:
                        # Extract URL between first quote after HYPERLINK( and the next quote or semicolon
                        url_start = formula.find('"', formula.find('HYPERLINK(')) + 1
                        url_end = formula.find('"', url_start)
                        url = formula[url_start:url_end] if url_start > 0 and url_end > 0 else None

                        if url:
                            email_hyperlinks[idx] = url
                    except Exception as e:
                        print(f"Error extracting hyperlink from formula: {e}")

                # Also check if there's a direct hyperlink in the cell
                hyperlink = row['crm_mail']['hyperlink']
                if hyperlink:
                    email_hyperlinks[idx] = hyperlink

        # Calculate statistics
        total_leads = len(values_df)
        engaged_leads = values_df[(values_df['cust_activity'].astype(float) > 0) |
                               (values_df['wa_activity'].astype(float) > 0)].shape[0]
        engaged_percentage = round((engaged_leads / total_leads) * 100, 2) if total_leads > 0 else 0
        waiting_leads = values_df[values_df['crm_mail'] == 'CUSTOMER']
        waiting_count = len(waiting_leads)

        # Create HTML email content
        html_content = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                tr:nth-child(even) {{ background-color: #f9f9f9; }}
                .statistics {{ margin-bottom: 30px; }}
            </style>
        </head>
        <body>
            <h2>S-ARGAME CRM Daily Digest - {datetime.now().strftime('%Y-%m-%d')}</h2>

            <div class="statistics">
                <h3>Statistics</h3>
                <ul>
                    <li><strong>Total Leads:</strong> {total_leads}</li>
                    <li><strong>Engaged Leads:</strong> {engaged_leads} ({engaged_percentage}% of total)</li>
                    <li><strong>Waiting Leads:</strong> {waiting_count}</li>
                </ul>
            </div>

            <div class="hot-leads">
                <h3>Hot Leads (Waiting for Response)</h3>
        """

        if not waiting_leads.empty:
            html_content += """
                <table>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Last Activity</th>
                        <th>Type</th>
                    </tr>
            """

            # Sort waiting leads by last_activity date (if available)
            if 'last_activity' in waiting_leads.columns:
                waiting_leads = waiting_leads.sort_values(by='last_activity', ascending=False)

            for idx, (_, lead) in enumerate(waiting_leads.iterrows()):
                name = lead.get('nome', 'N/A')
                email = lead.get('email', 'N/A')
                phone = lead.get('phone_number', 'N/A')
                last_activity = lead.get('last_activity', 'N/A')
                customer_type = lead.get('business_or_home_user?', 'N/A')  # Get customer type (business/home)

                # Format the date as DD/MM/YYYY if it's not N/A
                if last_activity != 'N/A':
                    try:
                        # Attempt to parse the date and reformat it, stripping any time information
                        if isinstance(last_activity, str) and ' ' in last_activity:
                            # Split date and time if there's a space
                            last_activity = last_activity.split(' ')[0]
                        date_obj = datetime.strptime(str(last_activity), '%Y-%m-%d')
                        last_activity = date_obj.strftime('%d/%m/%Y')
                    except (ValueError, TypeError):
                        # If parsing fails, keep the original format
                        pass

                # Directly build WhatsApp link from phone number
                if phone and phone != 'N/A':
                    # Clean phone number - strip 'p:' prefix but keep the '+' sign
                    if isinstance(phone, str) and phone.startswith('p:'):
                        phone_number = phone[2:]  # Remove 'p:' prefix
                    else:
                        phone_number = str(phone)

                    # Create WhatsApp link
                    wa_link = f"https://wa.me/{phone_number}"
                else:
                    wa_link = "#"  # Default link if no valid phone

                # Get the original index of the row in the full dataframe
                original_idx = waiting_leads.index[idx]

                # Check if we have a hyperlink for this email
                email_link = email_hyperlinks.get(original_idx, f"mailto:{email}")

                html_content += f"""
                    <tr>
                        <td>{name}</td>
                        <td><a href="{email_link}">{email}</a></td>
                        <td><a href="{wa_link}">{phone}</a></td>
                        <td>{last_activity}</td>
                        <td>{customer_type}</td>
                    </tr>
                """

            html_content += """
                </table>
            """
        else:
            html_content += "<p>No waiting leads at the moment.</p>"

        html_content += """
            </div>
        </body>
        </html>
        """

        # Set up the sender and recipient emails
        sender_email = "S-ARGAME CRM <<EMAIL>>"
        recipient_email = DIGEST_EMAIL
        subject = f"S-ARGAME CRM Digest - {datetime.now().strftime('%Y-%m-%d')}"

        try:
            # Create a plain text version for the digest
            plain_text = f"""S-ARGAME CRM Daily Digest
{datetime.now().strftime('%Y-%m-%d')}

Statistics and hot leads for your review."""

            # Use the new helper function to create a properly formatted MIME message
            mime_message = create_html_body(
                sender_email=sender_email,
                recipient_email=recipient_email,
                subject=subject,
                html_body=html_content,
                plain_text=plain_text
            )

            # Directly call the create_mail function from gmail_send module
            message_id = create_mail(
                sender_email=sender_email,
                recipient_email=recipient_email,
                subject=subject,
                message_body=mime_message,
                is_html=True,
                draft=False  # Send directly
            )

            if message_id:
                print(f"Email sent successfully! Message ID: {message_id}")
            else:
                raise Exception("Email sending failed - no message ID returned")

        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            return {'success': False, 'error': f'Unexpected error: {str(e)}'}

        return {
            'success': True,
            'message': 'Daily digest generated and sent successfully'
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def main():
    print("Starting send_daily_digest.py")

    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Send daily digest email with CRM statistics.")
    parser.add_argument("--force", action="store_true", help="Force sending digest even if one was sent in the last 24 hours")
    args = parser.parse_args()

    # Check if we should send a digest today or if --force flag is used
    if args.force or should_send_digest():
        if args.force:
            print("Force flag detected. Sending daily digest regardless of last send time...")
        else:
            print("Sending daily digest...")

        result = send_daily_digest()

        if result['success']:
            # Update the timestamp since we successfully sent a digest
            update_digest_timestamp()
            print("Daily digest sent successfully")
        else:
            print(f"Failed to send daily digest: {result.get('error', 'Unknown error')}")
    else:
        print("Daily digest already sent within the last 24 hours. Skipping.")

    print("Completed send_daily_digest.py")

if __name__ == "__main__":
    main()