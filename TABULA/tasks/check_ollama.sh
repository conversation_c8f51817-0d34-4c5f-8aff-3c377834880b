#!/bin/bash
# OLLAMA AVAILABILITY CHECK AND STARTUP UTILITY
# This script ensures Ollama is running and has models loaded
# Can be used as a standalone utility or sourced by other scripts

# Function to log messages with timestamp
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $@"
}

# Function to check if Ollama service is running
check_ollama_service() {
    if ollama ps > /dev/null 2>&1; then
        return 0  # Service is running
    else
        return 1  # Service is not running
    fi
}

# Function to start Ollama service
start_ollama_service() {
    log_message "Ollama service is not running. Attempting to start..."
    
    # Try to start ollama in the background
    ollama serve > /dev/null 2>&1 &
    OLLAMA_PID=$!
    
    # Wait a bit for the service to start
    sleep 5
    
    # Check if the service is now running
    local retries=0
    local max_retries=12  # Wait up to 60 seconds (12 * 5 seconds)
    
    while [ $retries -lt $max_retries ]; do
        if check_ollama_service; then
            log_message "Ollama service started successfully (PID: $OLLAMA_PID)"
            return 0
        fi
        
        retries=$((retries + 1))
        log_message "Waiting for Ollama service to start... (attempt $retries/$max_retries)"
        sleep 5
    done
    
    log_message "ERROR: Failed to start Ollama service after $max_retries attempts"
    return 1
}

# Function to check and load models
check_and_load_models() {
    local model_name="${1:-gemma2:2b}"  # Default model
    
    # Check if any models are currently loaded
    local running_models=$(ollama ps | grep -v "NAME" | wc -l)
    
    if [ $running_models -eq 0 ]; then
        log_message "No ollama models are currently running. Starting $model_name..."
        
        # Start the model in the background
        ollama run "$model_name" "hello" > /dev/null 2>&1 &
        
        log_message "Waiting for model to load..."
        sleep 10
        
        # Verify the model is now running
        running_models=$(ollama ps | grep -v "NAME" | wc -l)
        if [ $running_models -eq 0 ]; then
            log_message "ERROR: Failed to start ollama model '$model_name'"
            return 1
        fi
        
        log_message "Ollama model '$model_name' is now running"
    else
        log_message "Ollama models are already running ($running_models model(s) active)"
    fi
    
    return 0
}

# Function to verify Ollama is fully operational
verify_ollama_operational() {
    log_message "Verifying Ollama is fully operational..."
    
    # Check service is running
    if ! check_ollama_service; then
        log_message "ERROR: Ollama service verification failed"
        return 1
    fi
    
    # Check models are loaded
    local running_models=$(ollama ps | grep -v "NAME" | wc -l)
    if [ $running_models -eq 0 ]; then
        log_message "ERROR: No models are running"
        return 1
    fi
    
    log_message "Ollama verification successful: service running with $running_models model(s)"
    return 0
}

# Main execution function
main() {
    local model_name="${1:-gemma2:2b}"  # Allow model name as parameter
    
    log_message "####################### CHECKING OLLAMA #######################"
    
    # Step 1: Check if Ollama service is running
    if ! check_ollama_service; then
        # Try to start the service
        if ! start_ollama_service; then
            log_message "ERROR: Unable to start Ollama service. Please check your Ollama installation."
            exit 1
        fi
    else
        log_message "Ollama service is already running"
    fi
    
    # Step 2: Check and load models if needed
    if ! check_and_load_models "$model_name"; then
        log_message "ERROR: Failed to ensure models are loaded"
        exit 1
    fi
    
    # Step 3: Final verification
    if ! verify_ollama_operational; then
        log_message "ERROR: Ollama operational verification failed"
        exit 1
    fi
    
    log_message "Ollama is ready and operational"
    log_message "################################################################"
    
    return 0
}

# Execute main function if script is run directly (not sourced)
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
