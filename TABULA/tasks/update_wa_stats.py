#!/usr/bin/env python3
# Script to update WhatsApp stats in CRM sheet
import os
import sys
import datetime
import re
from collections import Counter
from prettytable import PrettyTable

# Add parent directory to path to import utils
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from utils import google_credentials
from utils import google_sheets
from utils.CONFIG import CRM_SPREADSHEET_ID, CRM_SHEET_NAME

# Regular expression to validate phone numbers (+ followed by digits only)
PHONE_NUMBER_PATTERN = re.compile(r'^\+\d+$')

def update_wa_stats():
    """
    Update WhatsApp stats in the CRM sheet based on activity logged in whatsapp_numbers.txt

    This function:
    1. Reads phone numbers from logs/whatsapp_numbers.txt
    2. Counts occurrences of each valid phone number (+ followed by digits only)
    3. Retrieves CRM data from Google Sheets
    4. For each phone number in the CRM that matches a WhatsApp number:
       - Takes the current value in the "wa_activity" column
       - Adds the new count to it
       - Updates that specific cell in the CRM sheet
    5. Finally truncates the log file after processing
    """
    # Path to the WhatsApp numbers log file
    log_file_path = os.path.join(parent_dir, 'logs', 'whatsapp_numbers.txt')

    # Check if log file exists
    if not os.path.exists(log_file_path):
        print(f"Log file not found: {log_file_path}")
        return

    # Read and filter phone numbers from log file
    valid_phone_numbers = []
    invalid_entries = []

    with open(log_file_path, 'r') as log_file:
        for line in log_file:
            line = line.strip()
            if not line:
                continue

            # Check if the line is a valid phone number format (+ followed by digits only)
            if PHONE_NUMBER_PATTERN.match(line):
                valid_phone_numbers.append(line)
            else:
                invalid_entries.append(line)

    # If no valid phone numbers were found, exit early
    if not valid_phone_numbers:
        print("No valid WhatsApp phone numbers found in log file.")
        if invalid_entries:
            print(f"Found {len(invalid_entries)} invalid entries that were skipped.")
        return

    # Count occurrences of each valid phone number
    number_counts = Counter(valid_phone_numbers)

    # Print summary of numbers found
    print(f"Found {len(valid_phone_numbers)} valid WhatsApp interactions from {len(number_counts)} unique numbers")
    '''
    if invalid_entries:
        print(f"Skipped {len(invalid_entries)} invalid entries: {', '.join(invalid_entries[:5])}" +
              (f"... and {len(invalid_entries) - 5} more" if len(invalid_entries) > 5 else ""))
    '''

    # Initialize Google Sheets API client
    sheets_service = google_sheets.get_sheets_service()

    if not sheets_service:
        print("Error: Could not initialize Google Sheets API client")
        return

    # Get all data from CRM sheet
    try:
        result = sheets_service.spreadsheets().values().get(
            spreadsheetId=CRM_SPREADSHEET_ID,
            range=f"{CRM_SHEET_NAME}!A:Z"
        ).execute()

        crm_data = result.get('values', [])

        if not crm_data:
            print("Error: Could not retrieve CRM data from Google Sheets")
            return

    except Exception as e:
        print(f"Error accessing Google Sheets: {e}")
        return

    # Get header row to find column indices
    headers = crm_data[0]
    try:
        # Find the "phone_number" and "wa_activity" columns in the CRM
        phone_col_idx = headers.index("phone_number")
        wa_activity_col_idx = headers.index("wa_activity")
        print(f"Found phone_number column at index {phone_col_idx} and wa_activity column at index {wa_activity_col_idx}")
    except ValueError as e:
        print(f"Error: Required column not found in CRM sheet: {e}")
        return

    # Create a table to display updates
    update_table = PrettyTable()
    update_table.field_names = ["Phone Number", "Previous Count", "New Messages", "Updated Count"]

    # Track which rows need to be updated
    updates = []
    matched_phones = set()

    # Process each row in the CRM data (skip header row)
    for row_idx, row in enumerate(crm_data[1:], start=2):  # Start from 2 to account for header row in Google Sheets
        # Skip rows that don't have enough columns
        if len(row) <= max(phone_col_idx, wa_activity_col_idx):
            continue

        # Get phone number from CRM
        try:
            crm_phone = row[phone_col_idx]
        except IndexError:
            continue  # Skip rows with missing phone column

        # Skip if phone is empty
        if not crm_phone:
            continue

        # Extract the actual phone number from the CRM format (p:+number)
        if crm_phone.startswith('p:'):
            crm_phone = crm_phone[2:]  # Remove the 'p:' prefix

        # Normalize phone number (remove spaces, ensure it starts with '+' if not already)
        if not crm_phone.startswith('+'):
            crm_phone = '+' + crm_phone
        crm_phone = crm_phone.replace(' ', '')

        # Check if this number has WhatsApp activity
        if crm_phone in number_counts:
            matched_phones.add(crm_phone)

            # Get current wa_activity count from the CRM
            try:
                if wa_activity_col_idx < len(row) and row[wa_activity_col_idx]:
                    current_count = int(row[wa_activity_col_idx])
                else:
                    current_count = 0
            except (ValueError, IndexError):
                current_count = 0

            # Calculate new count by ADDING the new messages to the existing count
            new_messages = number_counts[crm_phone]
            updated_count = current_count + new_messages

            # Add to updates list
            updates.append({
                'row': row_idx,
                'phone': crm_phone,
                'previous': current_count,
                'new_messages': new_messages,
                'updated': updated_count
            })

            # Add row to update table
            update_table.add_row([crm_phone, current_count, new_messages, updated_count])

    # Report on unmatched numbers
    unmatched_phones = set(number_counts.keys()) - matched_phones
    '''
    if unmatched_phones:
        print(f"\nWarning: {len(unmatched_phones)} phone numbers from WhatsApp logs were not found in CRM:")
        for phone in list(unmatched_phones)[:5]:  # Show only first 5
            print(f"  - {phone}")
        if len(unmatched_phones) > 5:
            print(f"  - ... and {len(unmatched_phones) - 5} more")
    '''

    # If there are updates to make
    if updates:
        print(f"\nUpdating {len(updates)} rows in CRM sheet:")
        print(update_table)

        # Perform the updates in Google Sheets
        success_count = 0
        for update in updates:
            # Update ONLY the wa_activity cell for this row
            cell_range = f"{CRM_SHEET_NAME}!{chr(65 + wa_activity_col_idx)}{update['row']}"

            # Using correct pattern to update cell
            try:
                sheets_service.spreadsheets().values().update(
                    spreadsheetId=CRM_SPREADSHEET_ID,
                    range=cell_range,
                    valueInputOption="RAW",
                    body={"values": [[update['updated']]]}
                ).execute()
                success_count += 1
            except Exception as e:
                print(f"Error updating cell {cell_range}: {e}")

        print(f"Successfully updated {success_count} rows in CRM sheet.")
    else:
        print("\nNo matching phone numbers found in CRM sheet.")

    # Truncate the log file
    with open(log_file_path, 'w') as log_file:
        pass

    print(f"Log file truncated: {log_file_path}")

def main():
    print("Starting WhatsApp stats update...")
    print(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

    update_wa_stats()

    print("\nWhatsApp stats update completed.")
    print(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

if __name__ == "__main__":
    main()