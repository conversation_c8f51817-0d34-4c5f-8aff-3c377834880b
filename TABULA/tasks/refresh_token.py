#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to refresh Google API authentication.

This script triggers the OAuth flow for Google API authentication
by calling the get_google_credentials function with default arguments.
It can be run periodically to ensure the token remains valid.
"""

import os
import sys
# Add the parent directory to the path to import the utils module
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.google_credentials import get_google_credentials

def delete_token_file():
    """Delete the token file if it exists."""
    token_path = os.path.join(os.path.dirname(__file__), '..', 'token', 'token.json')
    if os.path.exists(token_path):
        try:
            os.remove(token_path)
            print(f"Deleted existing token file: {token_path}")
        except Exception as e:
            print(f"Error deleting token file: {e}")
    else:
        print("No existing token file found.")

def main():
    """
    Main function to refresh Google API credentials.
    
    Uses default arguments for the get_google_credentials function
    to trigger authentication flow if needed.
    """
    print("Starting Google API authentication refresh process...")
    
    # Delete the existing token file first
    delete_token_file()
    
    # Call get_google_credentials with default arguments
    # This will load existing credentials from the default token file
    # or trigger the OAuth flow if credentials are missing or invalid
    creds = get_google_credentials()
    
    if creds:
        print("Authentication successful.")
        print(f"Token is valid and will expire at: {creds.expiry}")
        print("The token has been saved to the default location.")
    else:
        print("Authentication failed. Please check error messages above.")

if __name__ == "__main__":
    main()
